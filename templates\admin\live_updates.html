<!-- Live Updates WebSocket Integration for Admin UI -->
<style>
.live-indicator {
    position: fixed;
    top: 10px;
    right: 20px;
    z-index: 9999;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.live-indicator.connected {
    background: linear-gradient(45deg, #28a745, #20c997);
    animation: pulse 2s infinite;
}

.live-indicator.connecting {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.live-indicator.disconnected {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
}

.live-indicator .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    margin-right: 8px;
    animation: blink 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.admin-stats-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    min-width: 250px;
    z-index: 9998;
    font-size: 13px;
    max-height: 300px;
    overflow-y: auto;
}

.admin-stats-widget h4 {
    margin: 0 0 10px 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    padding: 3px 0;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #333;
}

.recent-jobs {
    margin-top: 15px;
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.job-item {
    padding: 5px 0;
    border-bottom: 1px solid #f5f5f5;
    font-size: 11px;
}

.job-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
    font-size: 10px;
    margin-right: 5px;
}

.status-queued { background: #6c757d; }
.status-processing { background: #007bff; }
.status-completed { background: #28a745; }
.status-failed { background: #dc3545; }
.status-review { background: #ffc107; color: #333; }

.toggle-stats {
    position: absolute;
    top: 5px;
    right: 5px;
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #666;
}
</style>

<!-- Live Indicator -->
<div id="live-indicator" class="live-indicator connecting">
    <span class="dot"></span>
    <span>Connecting...</span>
</div>

<!-- Admin Stats Widget -->
<div id="admin-stats-widget" class="admin-stats-widget" style="display: none;">
    <button class="toggle-stats" onclick="toggleStatsWidget()" title="Hide Stats">×</button>
    <h4>📊 Live Queue Stats</h4>
    <div id="stats-content">
        <div class="stat-row">
            <span class="stat-label">Total Jobs:</span>
            <span class="stat-value" id="total-jobs">-</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">⏳ Queued:</span>
            <span class="stat-value" id="queued-jobs">-</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">🔄 Processing:</span>
            <span class="stat-value" id="processing-jobs">-</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">✅ Completed:</span>
            <span class="stat-value" id="completed-jobs">-</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">❌ Failed:</span>
            <span class="stat-value" id="failed-jobs">-</span>
        </div>
        <div class="stat-row">
            <span class="stat-label">🔍 Review:</span>
            <span class="stat-value" id="review-jobs">-</span>
        </div>
    </div>
    
    <div class="recent-jobs">
        <h4>📋 Recent Jobs</h4>
        <div id="recent-jobs-list">
            <div style="color: #999; font-style: italic;">Loading...</div>
        </div>
    </div>
</div>

<script>
// Global WebSocket connection for admin live updates
let adminSocket = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 3000;

function connectAdminWebSocket() {
    const wsScheme = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    const wsPath = wsScheme + window.location.host + '/ws/admin/live/';
    
    adminSocket = new WebSocket(wsPath);
    
    adminSocket.onopen = function(e) {
        console.log('Admin WebSocket connected');
        reconnectAttempts = 0;
        updateConnectionStatus('connected', '🟢 Live Updates');
        showStatsWidget();
    };
    
    adminSocket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        if (data.type === 'initial_data' || data.type === 'admin_update') {
            updateAdminData(data.data);
        }
    };
    
    adminSocket.onclose = function(event) {
        console.log('Admin WebSocket closed');
        updateConnectionStatus('disconnected', '🔴 Offline');
        hideStatsWidget();
        
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            reconnectAttempts++;
            updateConnectionStatus('connecting', '🟡 Reconnecting...');
            setTimeout(connectAdminWebSocket, RECONNECT_DELAY);
        }
    };
    
    adminSocket.onerror = function(error) {
        console.error('Admin WebSocket error:', error);
        updateConnectionStatus('disconnected', '⚠️ Error');
    };
}

function updateConnectionStatus(status, text) {
    const indicator = document.getElementById('live-indicator');
    if (indicator) {
        indicator.className = 'live-indicator ' + status;
        const textSpan = indicator.querySelector('span:last-child');
        if (textSpan) textSpan.textContent = text;
    }
}

function updateAdminData(data) {
    if (data.overview) {
        document.getElementById('total-jobs').textContent = data.overview.total_jobs || 0;
        document.getElementById('queued-jobs').textContent = data.overview.queued || 0;
        document.getElementById('processing-jobs').textContent = data.overview.processing || 0;
        document.getElementById('completed-jobs').textContent = data.overview.completed || 0;
        document.getElementById('failed-jobs').textContent = data.overview.failed || 0;
        document.getElementById('review-jobs').textContent = data.overview.review || 0;
    }
    
    if (data.recent_jobs) {
        updateRecentJobs(data.recent_jobs);
    }
    
    // Trigger page-specific updates if available
    if (typeof updatePageData === 'function') {
        updatePageData(data);
    }
}

function updateRecentJobs(jobs) {
    const container = document.getElementById('recent-jobs-list');
    if (!container) return;
    
    if (jobs.length === 0) {
        container.innerHTML = '<div style="color: #999; font-style: italic;">No recent jobs</div>';
        return;
    }
    
    container.innerHTML = jobs.slice(0, 5).map(job => `
        <div class="job-item">
            <span class="job-status status-${job.status}">${job.status}</span>
            <strong>${job.order__first_name} ${job.order__surname}</strong>
            <br>
            <small>${job.location__location_name} • ${job.retry_count}/${job.max_retries} retries</small>
        </div>
    `).join('');
}

function showStatsWidget() {
    document.getElementById('admin-stats-widget').style.display = 'block';
}

function hideStatsWidget() {
    document.getElementById('admin-stats-widget').style.display = 'none';
}

function toggleStatsWidget() {
    const widget = document.getElementById('admin-stats-widget');
    widget.style.display = widget.style.display === 'none' ? 'block' : 'none';
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only connect if user is on admin pages
    if (window.location.pathname.includes('/admin/')) {
        connectAdminWebSocket();
    }
    
    // Add click handler to live indicator to toggle stats
    document.getElementById('live-indicator').addEventListener('click', toggleStatsWidget);
    
    // Clean up on page unload
    window.addEventListener('beforeunload', function() {
        if (adminSocket && adminSocket.readyState === WebSocket.OPEN) {
            adminSocket.close();
        }
    });
});
</script>
