from django.shortcuts import render # Keep this if needed for other views
from rest_framework import viewsets
from .models import Location
from orders.serializers import LocationSerializer

# --- Add this ViewSet ---
class LocationViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows locations to be viewed or edited.
    """
    queryset = Location.objects.all().order_by('-created_at')
    serializer_class = LocationSerializer
