# Generated by Django 5.1.6 on 2025-03-02 02:16

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0005_alter_order_location_alter_order_qr_code"),
    ]

    operations = [
        migrations.RenameField(
            model_name="order",
            old_name="customer_name",
            new_name="first_name",
        ),
        migrations.AddField(
            model_name="order",
            name="surname",
            field=models.CharField(default="Unknown", max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="criminalcheck",
            name="check_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("passed", "Passed"),
                    ("failed", "Failed"),
                    ("error", "Something went wrong with the search"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="BarbadosForm",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "residential_status",
                    models.CharField(
                        choices=[
                            ("permanent", "Permanent Address Is In Barbados"),
                            ("visitor", "Non-Resident/Visitor"),
                        ],
                        max_length=100,
                    ),
                ),
                (
                    "how_are_you_entering",
                    models.CharField(
                        choices=[("air", "Air"), ("sea", "sea")], max_length=100
                    ),
                ),
                ("airline", models.CharField(max_length=100)),
                ("flight_number", models.CharField(max_length=50)),
                ("vessel_name", models.CharField(max_length=100)),
                ("vessel_number", models.CharField(max_length=100)),
                ("departure_country", models.CharField(max_length=100)),
                ("departure_airport", models.CharField(max_length=100)),
                ("intended_arrival_date", models.DateField()),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "Male"), ("female", "Female")], max_length=100
                    ),
                ),
                ("country_of_birth", models.CharField(max_length=100)),
                ("date_of_birth", models.DateField()),
                ("nationality", models.CharField(max_length=100)),
                ("country_of_residence", models.CharField(max_length=100)),
                ("zip_code", models.CharField(max_length=50)),
                ("phone_number", models.CharField(max_length=50)),
                ("travel_document_number", models.CharField(max_length=100)),
                ("travel_document_expiry", models.DateField()),
                ("visited_countries", models.TextField()),
                (
                    "purpose_of_visit",
                    models.CharField(
                        choices=[
                            ("gatherin", "We Gatherin"),
                            ("surf_pro", "Surf Pro"),
                            ("in_transit", "In-Transit Passenger"),
                            ("welcome_stamp", "Barbados Welcome Stamp"),
                            ("business", "Business"),
                            ("conference", "Conference/Convention/Meeting"),
                            ("crew", "Crew"),
                            ("cruise", "Cruise"),
                            ("education", "Education"),
                            ("funeral", "Funeral"),
                            ("honeymoon", "Honeymoon/Wedding"),
                            ("medical", "Medical Attention"),
                            ("holiday", "Pleasure/Holiday/Vacation"),
                            ("returning", "Returning National"),
                            ("sports", "Sports"),
                            ("visiting", "Visiting Friends/Relatives"),
                            ("other", "Other"),
                        ],
                        max_length=100,
                    ),
                ),
                ("accommodation_name", models.CharField(max_length=100)),
                ("accommodation_address", models.CharField(max_length=200)),
                ("specify_accommodation", models.CharField(max_length=200)),
                ("permament_address", models.CharField(max_length=200)),
                ("parish", models.CharField(max_length=100)),
                ("length_of_stay", models.IntegerField()),
                (
                    "farm_question",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                ("lugguage", models.IntegerField()),
                (
                    "customs_question_1",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "customs_question_2",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "customs_question_3",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "customs_question_4",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "customs_question_5",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "passenger_allowance",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "order",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="barbados_form",
                        to="orders.order",
                    ),
                ),
            ],
        ),
    ]
