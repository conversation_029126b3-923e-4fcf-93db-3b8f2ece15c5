import sys
import os

# Activate virtualenv
activate_this = '/home/<USER>/virtualenv/JG-Innovations--master/3.9/bin/activate_this.py'
with open(activate_this) as f:
    exec(f.read(), dict(__file__=activate_this))

# Add your project path
sys.path.insert(0, '/home/<USER>/JG-Innovations--master')

# Set the correct Django settings module
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings'

# Get WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
