import React from 'react';
import Button from '@mui/material/Button';

function CustomButton({ children, onClick, variant = "contained", color = "primary", size = "medium", sx, ...rest }) {
    return (
        <Button
            variant={variant}
            color={color}
            size={size}
            onClick={onClick}
            sx={sx}
            {...rest}
        >
            {children}   
        </Button>
    );
}

export default CustomButton;