import React from 'react';
import Sidebar from './components/shared/Sidebar';
import { Box } from '@mui/material';


function Layout({ sidebarOpen, handleSidebarToggle, children}) {
    return (
        <div style={{ display: 'flex' }}>
            <Sidebar onToggle={handleSidebarToggle} />
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    p: 3,
                    ml: sidebarOpen ? '260px' : '60px',
                    transition: 'margin-left 0.3s'    
                }}
            >
                { children }    
            </Box>
        </div>
    );
}

export default Layout;