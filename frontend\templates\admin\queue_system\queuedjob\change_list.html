{% extends "admin/change_list.html" %}
{% load admin_urls static admin_list %}

{% block extrahead %}
{{ block.super }}
<style>
    .queue-dashboard {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .queue-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    .stat-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
    }
    .queued { color: #ffc107; }
    .processing { color: #17a2b8; }
    .completed { color: #28a745; }
    .failed { color: #dc3545; }
    .review { color: #fd7e14; }
    .requeued { color: #6f42c1; }
    .priority { color: #e83e8c; }
    
    .failure-reasons {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin-top: 15px;
    }
    .failure-reasons h4 {
        margin-top: 0;
        color: #dc3545;
    }
    .failure-reason-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #eee;
    }
    .failure-reason-item:last-child {
        border-bottom: none;
    }
    
    .review-queue-highlight {
        background-color: #fff3cd !important;
        border-left: 4px solid #ffc107 !important;
    }
    .failed-job-highlight {
        background-color: #f8d7da !important;
        border-left: 4px solid #dc3545 !important;
    }

    .view-job-btn {
        background: #007cba;
        color: white !important;
        padding: 4px 8px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 12px;
        margin-right: 8px;
        display: inline-block;
    }
    .view-job-btn:hover {
        background: #005a87;
        color: white !important;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content_title %}
<h1>Queue System Dashboard</h1>
{% endblock %}

{% block content %}
<!-- Enhanced Queue Job List with Dashboard Navigation -->
<div style="background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
    <h3 style="margin-top: 0; color: #333;">🎛️ Queue Management Dashboards</h3>
    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <a href="{% url 'queue_system:queue_overview' %}" style="background: #007cba; color: white; padding: 12px 20px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
            📊 Queue Overview
        </a>
        <a href="{% url 'queue_system:review_queue_dashboard' %}" style="background: #dc3545; color: white; padding: 12px 20px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
            🔍 Review Queue
        </a>
    </div>
    <p style="margin-bottom: 0; margin-top: 10px; color: #666; font-size: 14px;">
        Professional dashboards for queue monitoring and failure handling.
    </p>
</div>

{{ block.super }}
{% endblock %}

{% block result_list %}
<div class="queue-dashboard">
    <h2>📊 Queue Statistics</h2>
    
    <div class="queue-stats">
        <div class="stat-card">
            <div class="stat-number queued">{{ queued_count }}</div>
            <div class="stat-label">⏳ Queued</div>
        </div>
        <div class="stat-card">
            <div class="stat-number processing">{{ processing_count }}</div>
            <div class="stat-label">🔄 Processing</div>
        </div>
        <div class="stat-card">
            <div class="stat-number completed">{{ completed_count }}</div>
            <div class="stat-label">✅ Completed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number failed">{{ failed_count }}</div>
            <div class="stat-label">❌ Failed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number review">{{ review_count }}</div>
            <div class="stat-label">🔍 Under Review</div>
        </div>
        <div class="stat-card">
            <div class="stat-number requeued">{{ requeued_count }}</div>
            <div class="stat-label">🔄 Requeued</div>
        </div>
        <div class="stat-card">
            <div class="stat-number priority">{{ priority_count }}</div>
            <div class="stat-label">⚡ Priority</div>
        </div>
    </div>
    
    {% if failure_reasons %}
    <div class="failure-reasons">
        <h4>🚨 Top Failure Reasons</h4>
        {% for reason in failure_reasons %}
        <div class="failure-reason-item">
            <span>{{ reason.failure_reason|default:"Unknown" }}</span>
            <span class="badge">{{ reason.count }}</span>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div style="margin-top: 20px;">
        <h4>📍 Location Statistics</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
            {% for location in location_stats %}
            <div style="background: white; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px;">
                <h5 style="margin-top: 0;">{{ location.name }}</h5>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; font-size: 12px;">
                    <div>Queued: <strong class="queued">{{ location.queued }}</strong></div>
                    <div>Processing: <strong class="processing">{{ location.processing }}</strong></div>
                    <div>Completed: <strong class="completed">{{ location.completed }}</strong></div>
                    <div>Failed: <strong class="failed">{{ location.failed }}</strong></div>
                    <div>Review: <strong class="review">{{ location.review }}</strong></div>
                    <div>Requeued: <strong class="requeued">{{ location.requeued }}</strong></div>
                </div>
                <div style="margin-top: 10px; font-size: 11px; color: #6c757d;">
                    Workers: {{ location.active_workers }}/{{ location.max_workers }}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Highlight review queue jobs and add View buttons
    const rows = document.querySelectorAll('#result_list tbody tr');
    rows.forEach(row => {
        const statusCell = row.querySelector('td:nth-child(4)'); // Adjust based on column position
        if (statusCell) {
            const statusText = statusCell.textContent.trim();
            if (statusText.includes('🔍') || statusText.includes('Under Review')) {
                row.classList.add('review-queue-highlight');
            } else if (statusText.includes('❌') || statusText.includes('Failed')) {
                row.classList.add('failed-job-highlight');
            }
        }

        // Add View button to each row
        const firstCell = row.querySelector('td');
        if (firstCell) {
            // Extract job ID from the row
            const checkbox = row.querySelector('input[type="checkbox"]');
            if (checkbox && checkbox.value) {
                const jobId = checkbox.value;
                const viewBtn = document.createElement('a');
                viewBtn.href = `/queue/admin/job-details/${jobId}/`;
                viewBtn.className = 'view-job-btn';
                viewBtn.innerHTML = '👁️ View';
                viewBtn.title = 'View job details';
                viewBtn.target = '_blank'; // Open in new tab

                // Insert the button at the beginning of the first cell
                firstCell.insertBefore(viewBtn, firstCell.firstChild);
            }
        }
    });
});
</script>

{{ block.super }}
{% endblock %}
