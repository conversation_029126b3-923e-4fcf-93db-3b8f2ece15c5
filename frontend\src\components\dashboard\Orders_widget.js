import React, { useState, useEffect } from 'react';
import { Paper, Box, Typography, FormControl, Select, MenuItem } from '@mui/material';

export function MiniStatWidget({
    title,
    apiEndpoint,
    gradient,
    menuBg,
    prefix = '',
}) {
    const [period, setPeriod] = useState('Today');
    const [value, setValue] = useState('...');
    const [subtitle, setSubtitle] = useState('');

    useEffect(() => {
        const periodMap = {
            'Today': 'today',
            'This week': 'this week',
            'This month': 'this month',
        };
        setValue('...');
        setSubtitle('');
        fetch(`${apiEndpoint}?period=${periodMap[period]}`)
            .then(res => res.json())
            .then(data => {
                // Use total_orders, average_order_value or failed_attempts depending on widget
                let mainValue = data.total_orders
                    ?? data.average_order_value
                    ?? data.failed_attempts
                    ?? '...';
                // Round up average order value if this is the average order value widget
                if (apiEndpoint === "/api/average-order-value" && !isNaN(mainValue)) {
                    mainValue = Math.ceil(Number(mainValue));
                }
                setValue(mainValue);
                const percentageChange = data.percentage_change;
                let subtitleText = '';
                if (percentageChange !== null && percentageChange !== undefined) {
                    const sign = percentageChange >= 0 ? '+' : '';
                    if (period === 'Today') {
                        subtitleText = `${sign}${percentageChange}% from yesterday`;
                    } else if (period === 'This week') {
                        subtitleText = `${sign}${percentageChange}% from last week`;
                    } else if (period === 'This month') {
                        subtitleText = `${sign}${percentageChange}% from last month`;
                    }
                }
                setSubtitle(subtitleText);
            })
            .catch(() => {
                setValue('Error');
                setSubtitle('Could not load data');
            });
    }, [period, apiEndpoint]);

    return (
        <Paper
            elevation={0}
            sx={{
                p: 3,
                borderRadius: 3,
                minWidth: 320,
                background: gradient,
            }}
        >
            <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                    <Typography variant="body2" color="white" fontWeight="bold">{title}</Typography>
                    <Typography variant="h4" color="white" fontWeight="bold" mt={1}>{prefix}{value}</Typography>
                    {subtitle && <Typography variant="body2" color="white" mt={1}>{subtitle}</Typography>}
                </Box>
                <FormControl
                    size="small"
                    sx={{
                        width: 120,
                        background: menuBg,
                        borderRadius: 2,
                        mt: 0,
                        alignSelf: 'flex-start',
                    }}
                >
                    <Select
                        value={period}
                        onChange={e => setPeriod(e.target.value)}
                        sx={{
                            color: 'white',
                            '.MuiOutlinedInput-notchedOutline': { border: 0 },
                            '& .MuiSvgIcon-root': { color: 'white' },
                            fontSize: 12,
                            pl: 1,
                        }}
                        MenuProps={{
                            PaperProps: {
                                sx: {
                                    backgroundColor: menuBg,
                                    '& .MuiMenuItem-root': {
                                        fontSize: 12,
                                        color: '#ffffff',
                                    },
                                }
                            }
                        }}
                    >
                        <MenuItem value="Today">Today</MenuItem>
                        <MenuItem value="This week">This week</MenuItem>
                        <MenuItem value="This month">This month</MenuItem>
                    </Select>
                </FormControl>
            </Box>
        </Paper>
    );
}

export default function OrdersWidget() {
    return (
        <>
            <MiniStatWidget
                title="Total Orders"
                apiEndpoint="/api/total-orders"
                gradient="linear-gradient(-90deg, #FFD166 0%, #D79600 100%)"
                menuBg="rgb(220, 164, 33)"
            />
            <Box height={16} />
            <MiniStatWidget
                title="Average Order Value"
                apiEndpoint="/api/average-order-value"
                gradient="linear-gradient(-90deg, #6EE7B7 0%, #00A262 100%)"
                menuBg="rgb(0, 162, 98)"
                prefix="$"
            />
        </>
    );
}