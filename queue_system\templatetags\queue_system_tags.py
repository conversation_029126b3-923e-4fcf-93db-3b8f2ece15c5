from django import template
from django.utils import timezone
from datetime import <PERSON>elta
import json

register = template.Library()

@register.filter
def get_range(value):
    """
    Returns a range of numbers from 0 to value
    """
    return range(int(value))

@register.filter
def mul(value, arg):
    """
    Multiplies the value by the argument
    """
    return float(value) * float(arg)

@register.filter
def div(value, arg):
    """
    Divides the value by the argument
    """
    if float(arg) == 0:
        return 0
    return float(value) / float(arg)

@register.filter
def sub(value, arg):
    """
    Subtracts the argument from the value
    """
    return int(value) - int(arg)

@register.filter
def pprint(value):
    """
    Pretty print JSON data
    """
    try:
        if value is None:
            return "null"

        if isinstance(value, str):
            # If it's already a string, try to parse it as JSON first
            try:
                parsed = json.loads(value)
                return json.dumps(parsed, indent=2, ensure_ascii=False)
            except (json.JSONDecodeError, TypeError):
                # If it's not valid JSON, return as is
                return value
        else:
            # If it's a dict or other object, format it as JSON
            return json.dumps(value, indent=2, ensure_ascii=False, default=str)
    except (TypeError, ValueError):
        # If all else fails, return string representation
        return str(value)

@register.filter
def debug_type(value):
    """
    Debug filter to show the type and value of a variable
    """
    return f"Type: {type(value).__name__}, Value: {repr(value)}"
