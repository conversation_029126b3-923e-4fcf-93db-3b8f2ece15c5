from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ..models import order, Location
from django.conf import settings
import hmac
import hashlib
import logging

logger = logging.getLogger(__name__)

class GravityFormsWebhookView(APIView):
    """
    API endpoint that receives webhooks from Gravity Forms
    """
    
    def verify_signature(self, request):
        # Implement signature verification if Gravity Forms supports it
        # This is a security measure to ensure requests are coming from your WordPress site
        return True  # Replace with actual verification
    
    def post(self, request):
        # Verify the webhook is from your WordPress site
        if not self.verify_signature(request):
            return Response({"error": "Invalid signature"}, status=status.HTTP_403_FORBIDDEN)
        
        try:
            # Extract form data from the webhook payload
            form_data = request.data
            logger.info(f"Received Gravity Forms webhook: {form_data}")
            
            # Map Gravity Forms fields to your order model fields
            # Adjust field names based on your actual Gravity Form structure
            location_id = form_data.get('location_id')
            
            # Get the location object
            try:
                location = Location.objects.get(id=location_id)
            except Location.DoesNotExist:
                return Response(
                    {"error": f"Location with ID {location_id} not found"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Create a new order
            new_order = order.objects.create(
                first_name=form_data.get('first_name'),
                surname=form_data.get('surname'),
                customer_email=form_data.get('email'),
                location=location,
                status='pending'  # Default status for new orders
            )
            
            # The order creation will trigger the signal to create criminal checks
            
            return Response({
                "success": True,
                "message": "Order created successfully",
                "order_id": str(new_order.id)
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error processing Gravity Forms webhook: {str(e)}")
            return Response(
                {"error": f"Failed to process order: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )