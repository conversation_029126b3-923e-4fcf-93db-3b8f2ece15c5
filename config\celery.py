import os
from celery import Celery
from kombu import Queue
import logging
from task_validation import *

# Set the forked_by_multiprocessing variable only for windows environment
os.environ['FORKED_BY_MULTIPROCESSING'] = '1'

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
logger = logging.getLogger(__name__)

# Set the default Django settings module for the 'celery' program.

app = Celery('config')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

# Use database scheduler
app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

# Explicitly ensure Redis configuration is loaded
from django.conf import settings
app.conf.broker_url = settings.CELERY_BROKER_URL
app.conf.result_backend = settings.CELERY_RESULT_BACKEND

# Force Redis transport (fix for AMQP connection attempts)
app.conf.broker_transport = 'redis'

# Define default queues
app.conf.task_default_queue = 'default'
app.conf.task_default_exchange = 'default'
app.conf.task_default_routing_key = 'default'

# Define a routing function


# Define queues explicitly
# app.conf.task_queues = (
#     Queue('default', Exchange('default'), routing_key='default'),
#     Queue('scheduler', Exchange('scheduler'), routing_key='scheduler'),
#     Queue('error', Exchange('error'), routing_key='error'),
#     # Dynamic location queues will be created as needed
# )

# from locations.models import Location
# def get_location_queues():
#     """Dynamically create queues for all locations"""
#     location_queues = [
#         Queue(f'location.{loc.id}', routing_key=f'location.{loc.id}')
#         for loc in Location.objects.all()
#     ]
#     return location_queues

# Define queues with explicit Redis transport
from kombu import Exchange

default_exchange = Exchange('default', type='direct')
scheduler_exchange = Exchange('scheduler', type='direct')
error_exchange = Exchange('error', type='direct')

app.conf.task_queues = (
    Queue('default', exchange=default_exchange, routing_key='default'),
    Queue('scheduler', exchange=scheduler_exchange, routing_key='scheduler'),
    Queue('error', exchange=error_exchange, routing_key='error'),
    # *get_location_queues()
)

def route_process_order(task_name, task_args, task_kwargs):
    location_id = task_kwargs.get('location_id')
    if location_id:
        return {'queue': f'location.{location_id}'}
    return {'queue': 'default'}

app.conf.task_routes = {
    'queue_system.tasks.check_waiting_queue': {'queue': 'scheduler'},
    'queue_system.tasks.process_failed_job': {'queue': 'error'},
    'queue_system.tasks.schedule_job': {'queue': 'scheduler'},
    'queue_system.tasks.sync_worker_status_task': {'queue': 'scheduler'},
    # process_order routing is handled manually in the send_task calls
    'queue_system.tasks.process_order': {'queue': 'default'}
}

# Configure task routing
# app.conf.task_routes = {
    # 'queue_system.tasks.process_order': {'queue': 'scheduler'},
    # 'config.tasks.process_order': 'queue_system.tasks.process_order',
    # 'config.tasks.process_order': route_process_order,
    # 'config.tasks.process_order': {'queue': 'scheduler'},
    # 'queue_system.routers.route_process_order'
    # 'queue_system.tasks.check_waiting_queue': {'queue': 'scheduler'},
    # 'queue_system.tasks.process_failed_job': {'queue': 'error'},
    # 'queue_system.tasks.schedule_job': {'queue': 'scheduler'},
# }

# Configure task routes for location-based queues
# app.conf.task_routes = [
#     'queue_system.routers.route_process_order'
# ]

app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']
app.conf.task_default_queue = 'default'
app.conf.task_create_missing_queues = True

app.conf.update(
    task_serializer='json',
    result_serializer='json',
    accept_content=['json'],
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_track_started=True,
    worker_max_tasks_per_child=100,
    broker_connection_retry_on_startup=True,
    # Explicitly set Redis transport options
    broker_transport='redis',
    broker_transport_options={
        'visibility_timeout': 3600,
        'fanout_prefix': True,
        'fanout_patterns': True,
        'master_name': None
    },
)

# app.conf.task_routes = {
#     'queue_system.tasks.check_waiting_queue': {'queue': 'scheduler'},
#     'queue_system.tasks.process_failed_job': {'queue': 'error'},
#     'queue_system.tasks.schedule_job': {'queue': 'scheduler'},
#     'queue_system.tasks.process_order': {'queue': 'default'},  # temporary
# }

app.conf.beat_schedule = {
    'check-waiting-queue-every-minute': {
        'task': 'queue_system.tasks.check_waiting_queue',
        'schedule': 60.0,  # Every minute
        'options': {'queue': 'scheduler'}
    },
    'sync-worker-status-every-30-seconds': {
        'task': 'queue_system.tasks.sync_worker_status_task',
        'schedule': 3.0,  # Every 3 seconds (reduced frequency)
        'options': {'queue': 'scheduler'}
    },
}






