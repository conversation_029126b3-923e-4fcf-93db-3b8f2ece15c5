from django.core.management.base import BaseCommand
from orders.models import CriminalCheck
from external.api_clients.opensanctions import OpenSanctionsClient
from django.conf import settings
import random


class Command(BaseCommand):
    help = 'Test OpenSanctions API with an existing order (simulation mode available)'

    def add_arguments(self, parser):
        parser.add_argument('check_id', type=str, help='ID of criminal check to test')
        parser.add_argument(
            '--simulation', 
            action='store_true',
            help='Run in simulation mode without making actual API calls'
        )
    
    def handle(self, *args, **options):
        check_id = options['check_id']
        simulation_mode = options['simulation']
        
        try:
            # Get the criminal check from database
            check = CriminalCheck.objects.get(id=check_id)
            name = f"{check.order.first_name} {check.order.surname}"
            self.stdout.write(f"Processing check for: {name}")
            
            if simulation_mode:
                # Simulation mode: always pass unless name contains "fail" for testing
                self.stdout.write(self.style.WARNING("Running in SIMULATION mode - no API calls made"))
                passed = not ("fail" in name.lower())  # Only fail if name contains "fail"
                
                # For demonstration purposes
                if passed:
                    self.stdout.write("Simulated check: PASSED")
                else:
                    self.stdout.write("Simulated check: FAILED (name contains 'fail')")
            else:
                # Real API mode
                client = OpenSanctionsClient(settings.OPENSANCTIONS_API_KEY)
                passed = client.check_person(name)
            
            # Update the check status
            old_status = check.check_status
            check.check_status = 'passed' if passed else 'failed'
            check.save()
            
            # Show the result
            self.stdout.write(self.style.SUCCESS(
                f"Check updated from '{old_status}' to '{check.check_status}'"
            ))
            
        except CriminalCheck.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Check with ID {check_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))

