import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets, serializers, generics
from rest_framework.decorators import api_view
from django.utils import timezone
from django.db.models import F, Sum
from .models import order, FailedSubmission, Location
from datetime import datetime, timedelta
from .serializers import OrderSerializer, LocationSerializer
from functools import wraps
# from rest_framework.permissions import IsAuthenticated
import traceback


logger = logging.getLogger(__name__)

# Helper function to safely get values from data
def safe_get(data, key, default=''):
    """Safely get a value from data, which could be a dict, list, or other object"""
    if isinstance(data, dict):
        return data.get(key, default)
    elif isinstance(data, list) and len(data) > 0:
        # If it's a list, try to find an item with the key
        for item in data:
            if isinstance(item, dict) and key in item:
                return item[key]
        # If not found in list items, return default
        return default
    else:
        # For any other type, return default
        return default

# --- Helper Functions ---

def get_percentage_change(current, previous):
    if previous == 0:
        return 100.0 if current > 0 else 0.0
    try:
        return round(((current - previous) / previous) * 100, 2)
    except ZeroDivisionError:
        return 0.0

def count_orders_in_range(start, end):
    return order.objects.filter(created_at__gte=start, created_at__lt=end).count()

def calculate_turnover_in_range(start, end):
    orders_in_range = order.objects.filter(created_at__gte=start, created_at__lt=end)
    gross_sum = orders_in_range.aggregate(total=Sum(F('location__traveller_price')))['total'] or 0
    cost_sum = orders_in_range.aggregate(total=Sum(F('location__cost_price')))['total'] or 0
    return gross_sum, cost_sum # Renamed second value


def get_period_range(period, reference_date=None):
    if not reference_date:
        reference_date = timezone.localtime(timezone.now())
    tz = timezone.get_current_timezone()

    if period == 'today':
        start = datetime.combine(reference_date.date(), datetime.min.time(), tzinfo=tz)
        end = start + timedelta(days=1)
    elif period == 'this week':
        start_of_week = reference_date - timedelta(days=reference_date.weekday())
        start = datetime.combine(start_of_week.date(), datetime.min.time(), tzinfo=tz)
        end = start + timedelta(days=7)
    elif period == 'this month':
        start = reference_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if start.month == 12:
            end = start.replace(year=start.year + 1, month=1, day=1)
        else:
            end = start.replace(month=start.month + 1, day=1)
    else:
        logger.warning(f"Invalid period requested: {period}")
        return None, None
    return start, end

def get_previous_period_range(period, current_start, now):
    start_previous, end_previous = None, None
    try:
        if period == 'today':
            previous_period_ref_date = now - timedelta(days=1)
            start_previous, end_previous = get_period_range('today', previous_period_ref_date)
        elif period == 'this week':
            previous_period_ref_date = current_start - timedelta(days=7)
            start_previous, end_previous = get_period_range('this week', previous_period_ref_date)
        elif period == 'this month':
            previous_period_ref_date = current_start - timedelta(days=1) # Go to last day of previous month
            start_previous, end_previous = get_period_range('this month', previous_period_ref_date)
    except Exception as e:
        logger.error(f"Error calculating previous period range for period '{period}': {e}")
    return start_previous, end_previous

def with_period_ranges(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        period = request.query_params.get('period', 'today').lower()
        now = timezone.localtime(timezone.now())
        start_current, end_current = get_period_range(period, now)
        if not start_current or not end_current:
            return Response({'error': 'Invalid period parameter.'}, status=status.HTTP_400_BAD_REQUEST)
        start_previous, end_previous = get_previous_period_range(period, start_current, now)
        return view_func(
            request, period, start_current, end_current, start_previous, end_previous, *args, **kwargs
        )
    return _wrapped_view

# --- API Views ---

class OrderDetailsView(APIView):
    permission_classes = [] # Assuming public access for now

    def _get_order_response(self, query):
        try:
            orders = order.objects.filter(**query)
            if not orders.exists():
                logger.info(f"No orders found for parameters: {query}")
                return Response(
                    {"error": "No details were found with those details."},
                    status=status.HTTP_404_NOT_FOUND
                )
            serializer = OrderSerializer(orders, many=True)
            order_data = serializer.data
            for i, order_item in enumerate(orders):
                related_form = None
                if hasattr(order_item, 'barbados_form'):
                    related_form = order_item.barbados_form
                    destination = 'Barbados'
                    if related_form:
                         order_data[i]['travel_details'] = {
                            'destination': destination,
                            'arrival_date': getattr(related_form, 'intended_arrival_date', None).strftime('%Y-%m-%d') if getattr(related_form, 'intended_arrival_date', None) else 'N/A',
                            'travel_document_number': getattr(related_form, 'travel_document_number', 'N/A'),
                            'length_of_stay': getattr(related_form, 'length_of_stay', 'N/A')
                         }
            response = {
                "success": True,
                "message": f"Found {len(order_data)} order(s) matching your search criteria.",
                "orders": order_data
            }
            logger.info(f"Found {orders.count()} order(s) matching query: {query}")
            return Response(response)
        except Exception as e:
            logger.exception(f"Error in order lookup for query {query}: {str(e)}")
            return Response(
                {"error": "An error occurred while retrieving order details"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _build_order_query(self, data_source):
        """
        Extracts search parameters, validates them, and builds the query dictionary.
        Returns the query dictionary or a Response object on error.
        """
        order_number = data_source.get('orderNumber')
        first_name = data_source.get('firstName')
        surname = data_source.get('surname')
        customer_email = data_source.get('customerEmail')

        logger.info(f"Order search request - Params: Order number={order_number}, First name={first_name}, Surname={surname}, Customer email={customer_email}")

        if not any([order_number, first_name, surname, customer_email]):
            logger.warning("Request is missing all search parameters")
            return Response(
                {"error": "At least one search parameter is required (orderNumber, firstName, surname, customerEmail)"},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = {}
        if order_number:
            try:
                query['id'] = int(order_number)
            except ValueError:
                 logger.warning(f"Invalid orderNumber format received: {order_number}")
                 return Response({"error": "Invalid orderNumber format."}, status=status.HTTP_400_BAD_REQUEST)
        if first_name:
            query['first_name__iexact'] = first_name
        if surname:
            query['surname__iexact'] = surname
        if customer_email:
            query['customer_email__iexact'] = customer_email

        return query # Return the built query dictionary

    def get(self, request):
        query_or_error = self._build_order_query(request.query_params)
        if isinstance(query_or_error, Response): # Check if the helper returned an error Response
            return query_or_error
        return self._get_order_response(query_or_error) # Pass the valid query dictionary

    def post(self, request):
        data = request.data
        logger.info(f"Retell function call (POST) - Full request data: {data}")
        if not data:
            logger.warning("No data received from Retell POST request")
            return Response({"error": "No data provided in POST request."}, status=status.HTTP_400_BAD_REQUEST)

        query_or_error = self._build_order_query(data)
        if isinstance(query_or_error, Response): # Check if the helper returned an error Response
            return query_or_error
        return self._get_order_response(query_or_error) # Pass the valid query dictionary


@api_view(['GET'])
@with_period_ranges
def total_orders(request, period, start_current, end_current, start_previous, end_previous):
    orders_current_period = count_orders_in_range(start_current, end_current)
    orders_previous_period = 0
    if start_previous and end_previous:
        orders_previous_period = count_orders_in_range(start_previous, end_previous)
    else:
        logger.warning(f"Could not determine previous period range for period '{period}'.")
    percentage_change = get_percentage_change(orders_current_period, orders_previous_period)
    logger.info(f"Total orders for period '{period}': {orders_current_period} (Change: {percentage_change}%)")
    return Response({
        'total_orders': orders_current_period,
        'percentage_change': percentage_change
    })

@api_view(['GET'])
@with_period_ranges
def turnover(request, period, start_current, end_current, start_previous, end_previous):
    gross_sum_current, cost_sum_current = calculate_turnover_in_range(start_current, end_current)
    net_turnover_current = gross_sum_current - cost_sum_current
    gross_sum_previous, cost_sum_previous = 0, 0
    if start_previous and end_previous:
        gross_sum_previous, cost_sum_previous = calculate_turnover_in_range(start_previous, end_previous)
    else:
        logger.warning(f"Could not determine previous period range for turnover calculation (period: '{period}')")
    net_turnover_previous = gross_sum_previous - cost_sum_previous
    percentage_change_gross = get_percentage_change(gross_sum_current, gross_sum_previous)
    percentage_change_net = get_percentage_change(net_turnover_current, net_turnover_previous)
    logger.info(f"Turnover for period '{period}': Gross={gross_sum_current}, Net={net_turnover_current} (Gross Change: {percentage_change_gross}%, Net Change: {percentage_change_net}%)")
    return Response({
        'gross_turnover': gross_sum_current,
        'net_turnover': net_turnover_current,
        'percentage_change_gross': percentage_change_gross,
        'percentage_change_net': percentage_change_net
    })

@api_view(['GET'])
@with_period_ranges
def average_order_value(request, period, start_current, end_current, start_previous, end_previous):
    gross_sum_current, _ = calculate_turnover_in_range(start_current, end_current)
    count_current = count_orders_in_range(start_current, end_current)
    avg_current = float(gross_sum_current) / count_current if count_current else 0
    avg_previous = 0
    if start_previous and end_previous:
        gross_sum_previous, _ = calculate_turnover_in_range(start_previous, end_previous)
        count_previous = count_orders_in_range(start_previous, end_previous)
        avg_previous = float(gross_sum_previous) / count_previous if count_previous else 0
    else:
        logger.warning(f"Could not determine previous period range for average order value calculation (period: '{period}')")
    percentage_change = get_percentage_change(avg_current, avg_previous)
    logger.info(f"Average order value for period '{period}': {avg_current} (Change: {percentage_change}%)")
    return Response({
        'average_order_value': round(avg_current, 2),
        'percentage_change': percentage_change
    })

@api_view(['GET'])
@with_period_ranges
def failed_submissions(request, period, start_current, end_current, start_previous, end_previous):
    count = FailedSubmission.objects.filter(last_attempt__gte=start_current, last_attempt__lt=end_current).count()
    previous_count = 0
    if start_previous and end_previous:
        previous_count = FailedSubmission.objects.filter(last_attempt__gte=start_previous, last_attempt__lt=end_previous).count()
    percentage_change = get_percentage_change(count, previous_count)

    return Response({
        'failed_attempts': count,
        'percentage_change': percentage_change
    })

class LocationViewSet(viewsets.ModelViewSet):
    queryset = Location.objects.all().order_by('-created_at')
    serializer_class = LocationSerializer

class LocationDropdownListView(generics.ListAPIView):
    queryset = Location.objects.all().order_by('location_name')
    serializer_class = LocationSerializer

class OrderViewSet(viewsets.ModelViewSet):
    """ViewSet for listing and retrieving orders"""
    queryset = order.objects.all().order_by('-created_at')
    serializer_class = OrderSerializer

@api_view(['GET'])
def order_status_choices(request):

    choices = order.STATUS_CHOICES
    statuses = [{'value': value, 'display': display} for value, display in choices]
    return Response(statuses)

class GravityFormsSyncView(APIView):
    permission_classes = [] # Assuming public access for now
    
    def post(self, request):
        """
        Sync order data with Gravity Forms.
        Expects a JSON payload with order details.
        """
        # Log the full request data
        logger.info(f"Gravity Forms sync request received: {request.data}")
        
        # Log the request data in a more readable format for debugging
        try:
            import json
            formatted_data = json.dumps(request.data, indent=2)
            logger.info(f"Gravity Forms sync request data (formatted):\n{formatted_data}")
        except Exception as e:
            logger.warning(f"Could not format request data for logging: {str(e)}")
        
        try:
            # Extract data from request
            data = request.data

            if not data:
                logger.warning("No data provided in Gravity Forms sync request")
                return Response(
                    {"error": "No data provided"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Map Gravity Forms fields to our model fields
            mapped_data = {
                'first_name': safe_get(data, '13', ''),                # First name (including middle names)
                'last_name': safe_get(data, '14', ''),                 # Last name
                'email': safe_get(data, '21', ''),                     # Email address
                'phone_number': safe_get(data, '205', ''),             # Phone Number
                'gender': safe_get(data, '92', ''),                    # Select your gender
                'date_of_birth': safe_get(data, '94', ''),             # Date of birth
                'destination': 'Barbados',                       # Always Barbados for this form
                'entry_method': safe_get(data, '127', 'Air'),          # How are you arriving in Barbados?
                'airline': safe_get(data, '128', ''),                  # Select your Airline
                'flight_number': safe_get(data, '130', ''),            # Flight number
                'cruise_company': safe_get(data, '134', ''),           # Select your Cruise company
                'cruise_number': safe_get(data, '132', ''),            # Cruise number
                'departure_airport': safe_get(data, '200', ''),        # Departure airport
                'residential_status': safe_get(data, '123', 'Visitor'), # Residential Status
                'purpose_of_visit': safe_get(data, '29', ''),          # What is the purpose of your visit?
                'accommodation_type': safe_get(data, '30', ''),        # What type of accommodation are you staying in?
                'accommodation_specify': safe_get(data, '125', ''),    # Please describe the accommodation type
                'accommodation_address': safe_get(data, '117', ''),    # Accommodation address
                'accommodation_name': safe_get(data, '119', ''),       # Accommodation name
                'permanent_address': safe_get(data, '124', ''),        # Permanent street address in Barbados
                'parish': safe_get(data, '121', ''),                   # Parish Church (If applicable)
                'length_of_stay': safe_get(data, '122', ''),           # How many nights are you staying in Barbados?
                'travel_document_number': safe_get(data, '126', ''),   # Passport number
                'travel_document_expiry': safe_get(data, '105', ''),   # Expiry date
                'country_of_birth': safe_get(data, '95', ''),          # Country of birth
                'passport_same_country': safe_get(data, '97', 'Yes'),  # Was your passport issued in the same country you were born in?
                'passport_country': safe_get(data, '98', ''),          # Select the country your passport was issued from
                'visited_other_countries': safe_get(data, '210', 'No'), # Have you visited any other countries in the last 21 days?
                'visited_countries': safe_get(data, '114', ''),        # Which countries have you visited in the last 21 days?
                'home_address': safe_get(data, '215', ''),             # Address (home)
                'home_address_line1': safe_get(data, '215.1', ''),     # Address Line 1
                'city': safe_get(data, '215.3', ''),                   # City
                'zip_code': safe_get(data, '215.5', ''),               # Zip Code
                'country_of_residence': safe_get(data, '215.6', ''),   # Country (of residence)
                'covid_symptoms': safe_get(data, '209', 'No Symptoms'), # COVID symptoms
                'farm_question': safe_get(data, '58', 'No'),           # Have you been on a farm in the last seven (7) days?
                'travel_arrangement': safe_get(data, '142', ''),       # I am arriving in Barbados
                'family_members': safe_get(data, '143', '0'),          # How many family members
                'customs_question_1': safe_get(data, '64', 'NO'),      # Customs question 1
                'customs_question_2': safe_get(data, '62', 'NO'),      # Customs question 2
                'customs_question_3': safe_get(data, '63', 'NO'),      # Customs question 3
                'customs_question_4': safe_get(data, '61', 'NO'),      # Customs question 4
                'customs_question_5': safe_get(data, '60', 'NO'),      # Customs question 5
                'luggage': safe_get(data, '156', '1'),                 # Total number of luggage
                'arrival_date': safe_get(data, '31', ''),              # Arrival date
                'departure_country': safe_get(data, '40', ''),         # Country of departure
                'payment_amount': safe_get(data, '207.2', safe_get(data, 'payment_amount', '0.00')), # Payment Amount
                'transaction_id': safe_get(data, 'transaction_id', ''),# Transaction ID
            }

            # Extract family members data based on the form structure
            family_members_data = []
            family_members_count = safe_get(data, '143', '0')

            if family_members_count != '0':
                logger.info(f"Processing family members data for {family_members_count} members")
                
                # First family member (fields 144-154)
                if family_members_count >= '1':
                    # Get gender and ensure proper format (male/female)
                    member1_gender = safe_get(data, '146', 'Male')
                    if member1_gender:
                        member1_gender = member1_gender.strip().lower()
                    
                    member1 = {
                        'first_name': safe_get(data, '144', ''),
                        'surname': safe_get(data, '145', ''),
                        'gender': member1_gender,
                        'country_of_birth': safe_get(data, '147', ''),
                        'date_of_birth': safe_get(data, '148', ''),
                        'passport_same_country': safe_get(data, '149', 'Yes'),
                        'passport_country': safe_get(data, '202', ''),  # Only if 149 is "No"
                        'travel_document_number': safe_get(data, '183', ''),
                        'travel_document_expiry': safe_get(data, '182', ''),
                        'visited_other_countries': safe_get(data, '211', 'No'),
                        'visited_countries': safe_get(data, '184', '')  # Only if 211 is "Yes"
                    }
                    family_members_data.append(member1)
                    logger.info(f"Added family member 1: {member1['first_name']} {member1['surname']}, Gender: {member1['gender']}")
                
                # Second family member (fields 157-166)
                if family_members_count >= '2':
                    # Get gender and ensure proper format (male/female)
                    member2_gender = safe_get(data, '160', 'Male')
                    if member2_gender:
                        member2_gender = member2_gender.strip().lower()
                    
                    member2 = {
                        'first_name': safe_get(data, '157', ''),
                        'surname': safe_get(data, '158', ''),
                        'gender': member2_gender,
                        'country_of_birth': safe_get(data, '161', ''),
                        'date_of_birth': safe_get(data, '164', ''),
                        'passport_same_country': safe_get(data, '165', 'Yes'),
                        'passport_country': safe_get(data, '201', ''),  # Only if 165 is "No"
                        'travel_document_number': safe_get(data, '187', ''),
                        'travel_document_expiry': safe_get(data, '185', ''),
                        'visited_other_countries': safe_get(data, '212', 'No'),
                        'visited_countries': safe_get(data, '188', '')  # Only if 212 is "Yes"
                    }
                    family_members_data.append(member2)
                    logger.info(f"Added family member 2: {member2['first_name']} {member2['surname']}, Gender: {member2['gender']}")
                
                # Third family member (fields 167-173)
                if family_members_count >= '3':
                    # Get gender and ensure proper format (male/female)
                    member3_gender = safe_get(data, '169', 'Male')
                    if member3_gender:
                        member3_gender = member3_gender.strip().lower()
                    
                    member3 = {
                        'first_name': safe_get(data, '167', ''),
                        'surname': safe_get(data, '168', ''),
                        'gender': member3_gender,
                        'country_of_birth': safe_get(data, '170', ''),
                        'date_of_birth': safe_get(data, '171', ''),
                        'passport_same_country': safe_get(data, '172', 'Yes'),
                        'passport_country': safe_get(data, '203', ''),  # Only if 172 is "No"
                        'travel_document_number': safe_get(data, '191', ''),
                        'travel_document_expiry': safe_get(data, '189', ''),
                        'visited_other_countries': safe_get(data, '213', 'No'),
                        'visited_countries': safe_get(data, '192', '')  # Only if 213 is "Yes"
                    }
                    family_members_data.append(member3)
                    logger.info(f"Added family member 3: {member3['first_name']} {member3['surname']}, Gender: {member3['gender']}")
                
                # Fourth family member (fields 174-180)
                if family_members_count >= '4':
                    # Get gender and ensure proper format (male/female)
                    member4_gender = safe_get(data, '176', 'Male')
                    if member4_gender:
                        member4_gender = member4_gender.strip().lower()
                    
                    member4 = {
                        'first_name': safe_get(data, '174', ''),
                        'surname': safe_get(data, '175', ''),
                        'gender': member4_gender,
                        'country_of_birth': safe_get(data, '177', ''),
                        'date_of_birth': safe_get(data, '178', ''),
                        'passport_same_country': safe_get(data, '179', 'Yes'),
                        'passport_country': safe_get(data, '150', ''),  # Only if 179 is "No"
                        'travel_document_number': safe_get(data, '152', ''),
                        'travel_document_expiry': safe_get(data, '153', ''),
                        'visited_other_countries': safe_get(data, '214', 'No'),
                        'visited_countries': safe_get(data, '154', '')  # Only if 214 is "Yes"
                    }
                    family_members_data.append(member4)
                    logger.info(f"Added family member 4: {member4['first_name']} {member4['surname']}, Gender: {member4['gender']}")

            # Add family members data to the mapped data
            mapped_data['family_members_data'] = family_members_data
            mapped_data['travel_arrangement'] = safe_get(data, '142', 'On my own')

            # Log the mapped data
            logger.info(f"Mapped data from Gravity Forms: {mapped_data}")

            # Extract basic customer information
            first_name = mapped_data.get('first_name')
            surname = mapped_data.get('last_name')
            customer_email = mapped_data.get('email')
            location_id = mapped_data.get('location_id')  # Extract location_id here
            
            # Validate required fields
            if not all([first_name, surname, customer_email]):
                missing_fields = []
                if not first_name: missing_fields.append('first_name')
                if not surname: missing_fields.append('last_name')
                if not customer_email: missing_fields.append('email')
                
                logger.warning(f"Missing required fields: {', '.join(missing_fields)}")
                return Response(
                    {"error": f"Missing required fields: {', '.join(missing_fields)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get or create the location
            try:
                from locations.models import Location
                location = None
                
                # 1. First try to get location by ID if provided
                if location_id:
                    try:
                        # Try to get location by ID (handles both integer IDs and UUIDs)
                        try:
                            # First try as integer ID
                            location = Location.objects.get(id=int(location_id))
                        except (ValueError, TypeError):
                            # If that fails, try as UUID
                            import uuid
                            try:
                                uuid_obj = uuid.UUID(location_id)
                                location = Location.objects.get(id=uuid_obj)
                            except (ValueError, TypeError):
                                # If both approaches fail, log and return error
                                logger.warning(f"Invalid location ID format: {location_id}")
                                return Response(
                                    {"error": f"Invalid location ID format: {location_id}"},
                                    status=status.HTTP_400_BAD_REQUEST
                                )
                        
                        logger.info(f"Found location by ID: {location.location_name}")
                    except Location.DoesNotExist:
                        logger.warning(f"Location with ID {location_id} not found")
                        # Don't return error here, continue to try other methods
                        pass
                
                # 2. If no location found by ID, try to find by destination name from mapped data
                if not location:
                    destination = mapped_data.get('destination')
                    if destination:
                        try:
                            location = Location.objects.filter(location_name__iexact=destination).first()
                            if location:
                                logger.info(f"Found location by name: {location.location_name}")
                        except Exception as e:
                            logger.error(f"Error finding location by name: {str(e)}")
                
                # 3. If still no location, use Barbados as default location
                if not location:
                    try:
                        # Try to find Barbados in the database
                        location = Location.objects.filter(location_name__iexact='Barbados').first()
                        if location:
                            logger.info(f"Using Barbados as default location: {location.location_name}")
                        else:
                            # If Barbados doesn't exist in the database, create it
                            logger.info("Creating Barbados as default location")
                            location = Location.objects.create(
                                location_name='Barbados',
                                description='Default Barbados location',
                                traveller_price=data.get('traveller_price', 0.00),
                                cost_price=data.get('cost_price', 0.00)
                            )
                            logger.info(f"Successfully created Barbados location with ID: {location.id}")
                    except Exception as e:
                        logger.error(f"Error setting up Barbados as default location: {str(e)}")
                        
                        # 4. If creating Barbados fails, fall back to any available location
                        location = Location.objects.first()
                        if location:
                            logger.info(f"Falling back to first available location: {location.location_name}")
                        else:
                            # 5. No locations available, create a generic one
                            logger.info("No locations found in database. Creating generic location.")
                            try:
                                location = Location.objects.create(
                                    location_name='Unknown Location',
                                    description='Automatically created generic location',
                                    traveller_price=data.get('traveller_price', 0.00),
                                    cost_price=data.get('cost_price', 0.00)
                                )
                                logger.info(f"Successfully created generic location with ID: {location.id}")
                            except Exception as create_loc_error:
                                logger.error(f"Error creating generic location: {str(create_loc_error)}")
                                return Response(
                                    {"error": f"Failed to create location: {str(create_loc_error)}"},
                                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                                )
            except Exception as e:
                logger.error(f"Error getting location: {str(e)}")
                return Response(
                    {"error": f"Error getting location: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            
            # Create a new order
            from .models import order
            new_order = order.objects.create(
                first_name=first_name,
                surname=surname,
                customer_email=customer_email,
                location=location,
                status='pending'  # Initial status
            )
            
            logger.info(f"Created new order: {new_order.id} for {first_name} {surname}")
            
            # Check if we need to create a Barbados form
            is_barbados = 'Barbados' == 'Barbados' or data.get('destination') == 'Barbados' or (location and location.location_name == 'Barbados')
            logger.info(f"Is Barbados destination: {is_barbados}, Destination: {mapped_data.get('destination')}, Location: {location.location_name if location else 'None'}")

            if is_barbados:
                try:
                    from .models import BarbadosForm
                    logger.info("Starting Barbados form creation")
                    
                    # Get the actual field names from the model
                    barbados_form_fields = [f.name for f in BarbadosForm._meta.get_fields()]
                    logger.info(f"Available fields in BarbadosForm model: {barbados_form_fields}")
                    
                    # Prepare data dictionary with only fields that exist in the model
                    form_data = {}

                    # Required relationship
                    form_data['order'] = new_order

                    # Basic information (check if fields exist before adding)
                    if 'residential_status' in barbados_form_fields:
                        form_data['residential_status'] = data.get('residential_status', 'Non-Resident/Visitor')

                    if 'how_are_you_entering' in barbados_form_fields:
                        entry_method = safe_get(data, '127', 'Air')
                        # Ensure proper capitalization
                        if entry_method:
                            entry_method = entry_method.strip().capitalize()
                        form_data['how_are_you_entering'] = entry_method

                    # Travel information
                    if 'departure_country' in barbados_form_fields:
                        departure_country = safe_get(data, 'departure_country', 'GBR')
                        # Ensure it's a valid country code from the choices
                        form_data['departure_country'] = departure_country

                    if 'departure_airport' in barbados_form_fields:
                        departure_airport = safe_get(data, 'departure_airport', 'LHR')
                        # Ensure it's a valid airport code from the choices
                        form_data['departure_airport'] = departure_airport

                    # Parse arrival date
                    if 'intended_arrival_date' in barbados_form_fields:
                        arrival_date = None
                        arrival_date_str = data.get('arrival_date')
                        if arrival_date_str:
                            try:
                                arrival_date = datetime.strptime(arrival_date_str, '%Y-%m-%d').date()
                            except ValueError:
                                arrival_date = (datetime.now() + timedelta(days=7)).date()
                        else:
                            arrival_date = (datetime.now() + timedelta(days=7)).date()
                        form_data['intended_arrival_date'] = arrival_date

                    # Personal information
                    if 'gender' in barbados_form_fields:
                        # Get the gender value from the form data
                        gender_value = safe_get(data, '92')  # Field ID 92 is Gender
                        if gender_value:
                            # Convert to camel case (first letter uppercase, rest lowercase)
                            # This handles any input format: 'MALE', 'male', 'Male', etc.
                            form_data['gender'] = gender_value.strip().capitalize()
                            logger.info(f"Setting gender to: {form_data['gender']}")
                        else:
                            logger.warning("No gender value provided in form data, defaulting to 'Male'")
                            form_data['gender'] = 'Male'

                    if 'country_of_birth' in barbados_form_fields:
                        form_data['country_of_birth'] = data.get('country_of_birth', 'United Kingdom')

                    # Parse date of birth
                    if 'date_of_birth' in barbados_form_fields:
                        dob = None
                        dob_str = data.get('date_of_birth')
                        if dob_str:
                            try:
                                dob = datetime.strptime(dob_str, '%Y-%m-%d').date()
                            except ValueError:
                                dob = (datetime.now() - timedelta(days=365*30)).date()
                        else:
                            dob = (datetime.now() - timedelta(days=365*30)).date()
                        form_data['date_of_birth'] = dob

                    if 'nationality' in barbados_form_fields:
                        form_data['nationality'] = data.get('nationality', 'British')

                    if 'country_of_residence' in barbados_form_fields:
                        form_data['country_of_residence'] = data.get('country_of_residence', 'United Kingdom')

                    if 'zip_code' in barbados_form_fields:
                        form_data['zip_code'] = data.get('zip_code', 'SW1A 1AA')

                    if 'phone_number' in barbados_form_fields:
                        form_data['phone_number'] = data.get('phone_number', '+44123456789')

                    # Travel document information
                    # Note: We're skipping travel_document_type since it caused an error

                    if 'travel_document_number' in barbados_form_fields:
                        form_data['travel_document_number'] = data.get('travel_document_number', 'P12345678')

                    # Parse travel document expiry
                    if 'travel_document_expiry' in barbados_form_fields:
                        travel_document_expiry = None
                        expiry_date_str = data.get('travel_document_expiry')
                        if expiry_date_str:
                            try:
                                travel_document_expiry = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
                            except ValueError:
                                travel_document_expiry = (datetime.now() + timedelta(days=365*10)).date()
                        else:
                            travel_document_expiry = (datetime.now() + timedelta(days=365*10)).date()
                        form_data['travel_document_expiry'] = travel_document_expiry

                    # Visited countries
                    if 'visited_countries' in barbados_form_fields:
                        form_data['visited_countries'] = data.get('visited_countries', 'None')

                    # Destination information
                    if 'purpose_of_visit' in barbados_form_fields:
                        form_data['purpose_of_visit'] = data.get('purpose_of_visit', 'Holiday')

                    if 'accommodation_type' in barbados_form_fields:
                        form_data['accommodation_type'] = data.get('accommodation_type', 'Hotel')

                    if 'accommodation_name' in barbados_form_fields:
                        form_data['accommodation_name'] = data.get('accommodation_name', 'Beach Resort')

                    if 'accommodation_address' in barbados_form_fields:
                        form_data['accommodation_address'] = data.get('accommodation_address', 'Bridgetown, Barbados')

                    if 'parish' in barbados_form_fields:
                        form_data['parish'] = data.get('parish', 'St. Michael')

                    # Stay information
                    if 'length_of_stay' in barbados_form_fields:
                        try:
                            form_data['length_of_stay'] = int(data.get('length_of_stay', 7))
                        except (ValueError, TypeError):
                            form_data['length_of_stay'] = 7

                    # Additional questions
                    if 'farm_question' in barbados_form_fields:
                        form_data['farm_question'] = data.get('farm_question', 'no')

                    if 'family_members' in barbados_form_fields:
                        form_data['family_members'] = data.get('family_members', '0')

                    if 'lugguage' in barbados_form_fields:  # Note the spelling "lugguage" not "luggage"
                        try:
                            form_data['lugguage'] = int(data.get('luggage', 1))
                        except (ValueError, TypeError):
                            form_data['lugguage'] = 1

                    # Customs questions
                    if 'customs_question_1' in barbados_form_fields:
                        form_data['customs_question_1'] = data.get('customs_question_1', 'NO')

                    if 'customs_question_2' in barbados_form_fields:
                        form_data['customs_question_2'] = data.get('customs_question_2', 'NO')

                    if 'customs_question_3' in barbados_form_fields:
                        form_data['customs_question_3'] = data.get('customs_question_3', 'NO')

                    if 'customs_question_4' in barbados_form_fields:
                        form_data['customs_question_4'] = data.get('customs_question_4', 'NO')

                    if 'customs_question_5' in barbados_form_fields:
                        form_data['customs_question_5'] = data.get('customs_question_5', 'NO')

                    # Passenger allowance
                    if 'passenger_allowance' in barbados_form_fields:
                        form_data['passenger_allowance'] = data.get('passenger_allowance', 'NO')

                    # For airline/vessel details
                    entry_method = data.get('entry_method', 'Air')
                    if entry_method == 'Air':
                        if 'airline' in barbados_form_fields:
                            form_data['airline'] = data.get('airline', 'British Airways')
                        if 'flight_number' in barbados_form_fields:
                            form_data['flight_number'] = data.get('flight_number', 'BA123')
                    else:  # Sea
                        if 'vessel_name' in barbados_form_fields:
                            form_data['vessel_name'] = data.get('vessel_name', 'Cruise Ship')
                        if 'vessel_number' in barbados_form_fields:
                            form_data['vessel_number'] = data.get('vessel_number', 'CS123')

                    logger.info(f"Creating Barbados form with fields: {form_data.keys()}")

                    # Create the Barbados form with only the fields that exist in the model
                    try:
                        barbados_form = BarbadosForm.objects.create(**form_data)
                        logger.info(f"Successfully created Barbados form with ID: {barbados_form.id}")
                        
                        # Handle family members if any
                        family_members_count = safe_get(data, '143', '0')
                        family_members_data = mapped_data.get('family_members_data', [])

                        if family_members_count != '0' and family_members_data:
                            from .models import BarbadosFamilyMembers
                            logger.info(f"Processing {len(family_members_data)} family members")
                            
                            # Get departure country and airport from the main form
                            departure_country = data.get('departure_country', 'GBR')  # Default to UK
                            departure_airport = data.get('departure_airport', 'LHR')  # Default to London Heathrow
                            
                            for i, member_data in enumerate(family_members_data):
                                try:
                                    # Extract family member details
                                    member_first_name = member_data.get('first_name', f'Family Member {i+1}')
                                    member_surname = member_data.get('surname', 'Surname')
                                    
                                    # Skip if both first name and surname are empty
                                    if not member_first_name and not member_surname:
                                        logger.warning(f"Skipping family member {i+1} due to missing name")
                                        continue
                                        
                                    # Ensure gender is in correct format (lowercase)
                                    member_gender = member_data.get('gender', 'male')
                                    if member_gender:
                                        # Convert to lowercase to match model choices
                                        member_gender = member_gender.strip().lower()
                                        logger.info(f"Setting gender for family member {i+1} to: {member_gender}")
                                    else:
                                        logger.warning(f"No gender value provided for family member {i+1}, defaulting to 'male'")
                                        member_gender = 'male'
                                    
                                    # Parse date of birth
                                    member_dob = None
                                    member_dob_str = member_data.get('date_of_birth')
                                    if member_dob_str:
                                        try:
                                            member_dob = datetime.strptime(member_dob_str, '%Y-%m-%d').date()
                                        except ValueError:
                                            try:
                                                member_dob = datetime.strptime(member_dob_str, '%d/%m/%Y').date()
                                            except ValueError:
                                                logger.warning(f"Could not parse date of birth for family member {i+1}: {member_dob_str}")
                                                member_dob = (datetime.now() - timedelta(days=365*20)).date()
                                    else:
                                        member_dob = (datetime.now() - timedelta(days=365*20)).date()
                                    
                                    # Use country of birth as nationality if available
                                    member_nationality = member_data.get('country_of_birth', 'British')
                                    member_travel_doc_num = member_data.get('travel_document_number', f'P{100000+i}')
                                    
                                    # Parse travel document expiry
                                    member_doc_expiry = None
                                    member_expiry_str = member_data.get('travel_document_expiry')
                                    if member_expiry_str:
                                        try:
                                            member_doc_expiry = datetime.strptime(member_expiry_str, '%Y-%m-%d').date()
                                        except ValueError:
                                            try:
                                                member_doc_expiry = datetime.strptime(member_expiry_str, '%d/%m/%Y').date()
                                            except ValueError:
                                                logger.warning(f"Could not parse document expiry for family member {i+1}: {member_expiry_str}")
                                                member_doc_expiry = (datetime.now() + timedelta(days=365*10)).date()
                                    else:
                                        member_doc_expiry = (datetime.now() + timedelta(days=365*10)).date()
                                    
                                    # Handle visited countries
                                    visited_other = member_data.get('visited_other_countries', 'No')
                                    member_visited_countries = 'None'
                                    if visited_other.lower() == 'yes':
                                        member_visited_countries = member_data.get('visited_countries', 'None')
                                    
                                    member_farm_question = member_data.get('farm_question', 'no').lower()
                                    
                                    # Create family member record
                                    family_member = BarbadosFamilyMembers.objects.create(
                                        barbados_form=barbados_form,
                                        first_name=member_first_name,
                                        surname=member_surname,
                                        gender=member_gender,
                                        date_of_birth=member_dob,
                                        nationality=member_nationality,
                                        travel_document_number=member_travel_doc_num,
                                        travel_document_expiry=member_doc_expiry,
                                        visited_countries=member_visited_countries,
                                        farm_question=member_farm_question
                                    )
                                    
                                    logger.info(f"Created family member {i+1}: {member_first_name} {member_surname}")
                                    
                                except Exception as member_error:
                                    logger.error(f"Error creating family member {i+1}: {str(member_error)}")
                                    logger.error(traceback.format_exc())
                                    # Continue with next family member
                            
                    except Exception as create_error:
                        logger.error(f"Error creating Barbados form: {str(create_error)}")
                        logger.error(traceback.format_exc())
                        raise create_error
                        
                except Exception as e:
                    logger.error(f"Error in Barbados form creation process: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Continue processing even if Barbados form creation fails
            else:
                logger.info("Skipping Barbados form creation - not a Barbados destination")
            
            # Return success response with the new order ID
            return Response({
                "success": True,
                "message": "Order data successfully synced with Gravity Forms",
                "order_id": str(new_order.id)
            })
            
        except KeyError as e:
            logger.error(f"Missing required field in form data: {e}")
            return Response(
                {"error": f"Missing required field: {e}"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        except Exception as e:
            logger.exception(f"Error syncing with Gravity Forms: {str(e)}")
            return Response(
                {"error": f"An error occurred while syncing with Gravity Forms: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# Delete all this code at the end of the file (outside of any class or function)
# Extract family members data based on the form structure
# family_members_data = []
# family_members_count = safe_get(data, '143', '0')

# if family_members_count != '0':
#     logger.info(f"Processing family members data for {family_members_count} members")
    
#     # First family member (fields 144-154)
#     if family_members_count >= '1':
#         member1 = {
#             'first_name': safe_get(data, '144', ''),
#             'surname': safe_get(data, '145', ''),
#             'gender': safe_get(data, '146', 'Male'),
#             'country_of_birth': safe_get(data, '147', ''),
#             'date_of_birth': safe_get(data, '148', ''),
#             'passport_same_country': safe_get(data, '149', 'Yes'),
#             'passport_country': safe_get(data, '202', ''),  # Only if 149 is "No"
#             'travel_document_number': safe_get(data, '183', ''),
#             'travel_document_expiry': safe_get(data, '182', ''),
#             'visited_other_countries': safe_get(data, '211', 'No'),
#             'visited_countries': safe_get(data, '184', '')  # Only if 211 is "Yes"
#         }
#         family_members_data.append(member1)
#         logger.info(f"Added family member 1: {member1['first_name']} {member1['surname']}")
    
#     # Second family member (fields 157-166)
#     if family_members_count >= '2':
#         member2 = {
#             'first_name': safe_get(data, '157', ''),
#             'surname': safe_get(data, '158', ''),
#             'gender': safe_get(data, '160', 'Male'),
#             'country_of_birth': safe_get(data, '161', ''),
#             'date_of_birth': safe_get(data, '164', ''),
#             'passport_same_country': safe_get(data, '165', 'Yes'),
#             'passport_country': safe_get(data, '201', ''),  # Only if 165 is "No"
#             'travel_document_number': safe_get(data, '187', ''),
#             'travel_document_expiry': safe_get(data, '185', ''),
#             'visited_other_countries': safe_get(data, '212', 'No'),
#             'visited_countries': safe_get(data, '188', '')  # Only if 212 is "Yes"
#         }
#         family_members_data.append(member2)
#         logger.info(f"Added family member 2: {member2['first_name']} {member2['surname']}")
    
#     # Third family member (fields 167-173)
#     if family_members_count >= '3':
#         member3 = {
#             'first_name': safe_get(data, '167', ''),
#             'surname': safe_get(data, '168', ''),
#             'gender': safe_get(data, '169', 'Male'),
#             'country_of_birth': safe_get(data, '170', ''),
#             'date_of_birth': safe_get(data, '171', ''),
#             'passport_same_country': safe_get(data, '172', 'Yes'),
#             'passport_country': safe_get(data, '203', ''),  # Only if 172 is "No"
#             'travel_document_number': safe_get(data, '191', ''),
#             'travel_document_expiry': safe_get(data, '189', ''),
#             'visited_other_countries': safe_get(data, '213', 'No'),
#             'visited_countries': safe_get(data, '192', '')  # Only if 213 is "Yes"
#         }
#         family_members_data.append(member3)
#         logger.info(f"Added family member 3: {member3['first_name']} {member3['surname']}")
    
#     # Fourth family member (fields 174-180)
#     if family_members_count >= '4':
#         member4 = {
#             'first_name': safe_get(data, '174', ''),
#             'surname': safe_get(data, '175', ''),
#             'gender': safe_get(data, '176', 'Male'),
#             'country_of_birth': safe_get(data, '177', ''),
#             'date_of_birth': safe_get(data, '178', ''),
#             'passport_same_country': safe_get(data, '179', 'Yes'),
#             'passport_country': safe_get(data, '150', ''),  # Only if 179 is "No"
#             'travel_document_number': safe_get(data, '152', ''),
#             'travel_document_expiry': safe_get(data, '153', ''),
#             'visited_other_countries': safe_get(data, '214', 'No'),
#             'visited_countries': safe_get(data, '154', '')  # Only if 214 is "Yes"
#         }
#         family_members_data.append(member4)
#         logger.info(f"Added family member 4: {member4['first_name']} {member4['surname']}")

# # Add family members data to the mapped data
# mapped_data['family_members_data'] = family_members_data
# mapped_data['travel_arrangement'] = safe_get(data, '142', 'On my own')






















