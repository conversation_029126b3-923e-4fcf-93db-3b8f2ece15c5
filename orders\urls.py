from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import API_view
from django.urls import path
from .API_view import (
    OrderDetailsView,
    total_orders,
    turnover,
    average_order_value,
    failed_submissions,
    OrderViewSet,
    order_status_choices,
    GravityFormsSyncView
)
from locations.views import LocationViewSet

router = DefaultRouter()
router.register(r'locations', LocationViewSet, basename='location')
router.register(r'orders', OrderViewSet, basename='order')  # Register orders endpoint

urlpatterns = [
    path('order-details', OrderDetailsView.as_view(), name='order_details'),
    path('total-orders', total_orders, name='total_orders'),
    path('turnover', turnover, name='turnover'),
    path('average-order-value', average_order_value, name='average_order_value'),
    path('failed-submissions', failed_submissions, name='failed_submissions'),
    path('order_status_choices/', order_status_choices, name='order_status_choices'),
    path('sync-gravity-forms', GravityFormsSyncView.as_view(), name='sync_gravity_forms'),
    # Custom dropdown view removed; use router at /locations/ under /api/
    path('', include(router.urls)),
]

