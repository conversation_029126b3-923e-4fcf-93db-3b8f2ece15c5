@echo off
echo 🚀 STARTING CELERY WORKERS FOR LOCATION QUEUES
echo ===============================================
echo This starts Celery workers for each location queue
echo Each location gets its own queue: location.location_id
echo Requires Redis to be running
echo Press Ctrl+C to stop all workers
echo ===============================================

python manage.py start_location_queues --workers-per-location=1 --beat

echo.
echo ✅ All Celery workers stopped
pause
