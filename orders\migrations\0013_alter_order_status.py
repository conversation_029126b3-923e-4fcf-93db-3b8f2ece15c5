# Generated by Django 4.2.21 on 2025-07-09 21:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0012_alter_barbadosform_country_of_birth_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('qr_generated', 'QR Generated'), ('criminal_check_passed', 'Criminal Check Passed'), ('criminal_check_failed', 'Criminal Check Failed'), ('bot_submission_retry', 'Bot Submission Retry'), ('bot_submission_failed', 'Bot Submission Failed'), ('bot_completed_form', 'Bot Completed Form'), ('refunded', 'Refunded'), ('completed', 'Completed')], default='pending', max_length=25),
        ),
    ]
