from django.contrib import admin
from django.utils.translation import gettext_lazy as _

class ReviewQueueFilter(admin.SimpleListFilter):
    title = _('Review Queue Status')
    parameter_name = 'review_status'

    def lookups(self, request, model_admin):
        return (
            ('needs_review', _('🔍 Needs Review')),
            ('in_review', _('👤 Under Review')),
            ('high_retries', _('⚠️ High Retry Count')),
            ('critical_errors', _('🚨 Critical Errors')),
            ('ready_to_requeue', _('✅ Ready to Requeue')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'needs_review':
            return queryset.filter(status='review')
        elif self.value() == 'in_review':
            return queryset.filter(status='review', reviewed_by__isnull=False)
        elif self.value() == 'high_retries':
            return queryset.filter(retry_count__gte=2)
        elif self.value() == 'critical_errors':
            critical_reasons = [
                'missing_form_data',
                'invalid_order_data',
                'bot_configuration_error',
                'website_structure_changed',
                'authentication_failed'
            ]
            return queryset.filter(failure_reason__in=critical_reasons)
        elif self.value() == 'ready_to_requeue':
            return queryset.filter(
                status='review',
                reviewed_by__isnull=False,
                review_notes__isnull=False
            )

class FailureReasonFilter(admin.SimpleListFilter):
    title = _('Failure Category')
    parameter_name = 'failure_category'

    def lookups(self, request, model_admin):
        return (
            ('data_issues', _('📋 Data Issues')),
            ('bot_issues', _('🤖 Bot Issues')),
            ('website_issues', _('🌐 Website Issues')),
            ('system_issues', _('⚙️ System Issues')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'data_issues':
            return queryset.filter(
                failure_reason__in=['missing_form_data', 'invalid_order_data']
            )
        elif self.value() == 'bot_issues':
            return queryset.filter(
                failure_reason__in=['bot_configuration_error', 'bot_execution_error']
            )
        elif self.value() == 'website_issues':
            return queryset.filter(
                failure_reason__in=['website_structure_changed', 'authentication_failed']
            )
        elif self.value() == 'system_issues':
            return queryset.filter(
                failure_reason__in=['network_error', 'unknown_error']
            )

class RetryCountFilter(admin.SimpleListFilter):
    title = _('Retry Status')
    parameter_name = 'retry_status'

    def lookups(self, request, model_admin):
        return (
            ('first_attempt', _('🆕 First Attempt')),
            ('retrying', _('🔄 Retrying')),
            ('max_retries', _('🚫 Max Retries Reached')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'first_attempt':
            return queryset.filter(retry_count=0)
        elif self.value() == 'retrying':
            return queryset.filter(retry_count__gt=0, retry_count__lt=3)
        elif self.value() == 'max_retries':
            return queryset.filter(retry_count__gte=3)
