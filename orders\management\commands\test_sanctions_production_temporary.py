from django.core.management.base import BaseCommand
from orders.models import CriminalCheck
from external.api_clients.opensanctions import OpenSanctionsClient
from django.conf import settings


class Command(BaseCommand):
    help = 'Test OpenSanctions API with an existing order'

    def add_arguments(self, parser):
        parser.add_argument('check_id', type=str, help='ID of criminal check to test')
    
    def handle(self, *args, **options):
        check_id = options['check_id']
        
        try:
            # Get the criminal check from database
            check = CriminalCheck.objects.get(id=check_id)
            self.stdout.write(f"Processing check for: {check.order.customer_name}") ##customer_name needs changed-----------------------------
            
            # Create the API client
            client = OpenSanctionsClient(settings.OPENSANCTIONS_API_KEY)
            
            # Get the person's name and run the check
            name = check.order.customer_name ##customer_name needs changed -----------------------------
            passed = client.check_person(name)
            
            # Update the check status
            old_status = check.check_status
            check.check_status = 'passed' if passed else 'failed'
            check.save()
            
            # Show the result
            self.stdout.write(self.style.SUCCESS(
                f"Check updated from '{old_status}' to '{check.check_status}'"
            ))
            
        except CriminalCheck.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Check with ID {check_id} not found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {str(e)}"))