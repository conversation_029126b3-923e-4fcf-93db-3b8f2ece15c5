from django.contrib import admin
from .models import order, CriminalCheckDatabase, CriminalCheck, FailedSubmission, BarbadosForm, BarbadosFamilyMembers

class CriminalCheckInline(admin.TabularInline):
    model = CriminalCheck
    extra = 0

@admin.register(order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'surname', 'customer_email', 'status', 'location', 'created_at')
    list_filter = ('status', 'location')
    search_fields = ('first_name', 'surname', 'customer_email')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [CriminalCheckInline]

@admin.register(CriminalCheckDatabase)
class CriminalCheckDatabaseAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')

@admin.register(CriminalCheck)
class CriminalCheckAdmin(admin.ModelAdmin):
    list_display = ('id', 'order', 'database', 'check_status', 'checked_at')
    list_filter = ('check_status', 'database')
    search_fields = ('order__first_name',)

@admin.register(FailedSubmission)
class FailedSubmissionAdmin(admin.ModelAdmin):
    list_display = ('order', 'retry_attempts', 'last_attempt')
    list_filter = ('retry_attempts',)
    search_fields = ('order__first_name', 'error_message')

# Add this inline class
class BarbadosFamilyMembersInline(admin.TabularInline):
    model = BarbadosFamilyMembers
    extra = 0
    fields = ('first_name', 'surname', 'gender', 'date_of_birth', 'nationality')

@admin.register(BarbadosForm)
class BarbadosFormAdmin(admin.ModelAdmin):
    list_display = ('order', 'residential_status', 'how_are_you_entering', 'airline', 
                   'flight_number')  # Shortened for clarity
    list_filter = ('residential_status', 'gender', 'nationality')
    search_fields = ('order__first_name', 'order__surname', 'travel_document_number')
    inlines = [BarbadosFamilyMembersInline]

# Add this class
@admin.register(BarbadosFamilyMembers)
class BarbadosFamilyMembersAdmin(admin.ModelAdmin):
    list_display = ('first_name', 'surname', 'gender', 'nationality')
    list_filter = ('gender', 'nationality')
    search_fields = ('first_name', 'surname', 'travel_document_number')


# Register your models here.
