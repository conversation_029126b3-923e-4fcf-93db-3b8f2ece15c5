{% extends "admin/base_site.html" %}
{% load static %}
{% load queue_system_tags %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .job-details {
        padding: 20px;
    }
    .job-header {
        background: white;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .job-status {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 14px;
    }
    .status-queued { background: #fff3cd; color: #856404; }
    .status-processing { background: #d1ecf1; color: #0c5460; }
    .status-completed { background: #d4edda; color: #155724; }
    .status-failed { background: #f8d7da; color: #721c24; }
    .status-review { background: #ffeaa7; color: #856404; }
    .status-requeued { background: #e2e3e5; color: #383d41; }
    .status-cancelled { background: #f8d7da; color: #721c24; }
    
    .details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    .detail-section {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
    }
    .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 4px 0;
    }
    .detail-label {
        font-weight: 500;
        color: #666;
    }
    .detail-value {
        color: #333;
        text-align: right;
    }
    .error-section {
        background: #fff5f5;
        border: 1px solid #fed7d7;
    }
    .error-message {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
        margin-bottom: 10px;
    }
    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 20px;
    }
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
    }
    .btn-primary { background: #007cba; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: #212529; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-secondary { background: #6c757d; color: white; }
    .btn:hover { opacity: 0.9; }
    .timeline {
        position: relative;
        padding-left: 20px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 8px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 15px;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -24px;
        top: 6px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #007cba;
    }
    .timeline-time {
        font-size: 12px;
        color: #666;
        margin-bottom: 2px;
    }
    .timeline-event {
        font-weight: 500;
        color: #333;
    }
    .json-display {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        font-family: 'Courier New', Consolas, monospace;
        font-size: 13px;
        white-space: pre-wrap;
        word-break: break-word;
        max-height: 400px;
        overflow-y: auto;
        overflow-x: auto;
        line-height: 1.5;
    }
    .json-display pre {
        margin: 0;
        padding: 0;
        background: none;
        border: none;
        font-family: inherit;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
</style>
{% endblock %}

{% block content %}
<div class="job-details">
    <!-- Job Header -->
    <div class="job-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>📋 Job #{{ job.id }}</h1>
                <p style="margin: 5px 0; color: #666;">
                    Customer: {{ job.order.first_name }} {{ job.order.surname }} | 
                    Location: {{ job.location.location_name }}
                </p>
            </div>
            <div>
                <span class="job-status status-{{ job.status }}">
                    {% if job.status == 'queued' %}⏳{% elif job.status == 'processing' %}🔄{% elif job.status == 'completed' %}✅{% elif job.status == 'failed' %}❌{% elif job.status == 'review' %}🔍{% elif job.status == 'requeued' %}🔄{% elif job.status == 'cancelled' %}🚫{% endif %}
                    {{ job.get_status_display }}
                </span>
            </div>
        </div>
    </div>
    
    <!-- Details Grid -->
    <div class="details-grid">
        <!-- Basic Information -->
        <div class="detail-section">
            <div class="section-title">📊 Basic Information</div>
            <div class="detail-row">
                <span class="detail-label">Job ID:</span>
                <span class="detail-value">#{{ job.id }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Order ID:</span>
                <span class="detail-value">{{ job.order.id }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Location:</span>
                <span class="detail-value">{{ job.location.location_name }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Priority:</span>
                <span class="detail-value">
                    {% if job.priority_flag %}⚡ High{% else %}Normal{% endif %}
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Worker ID:</span>
                <span class="detail-value">{{ job.worker_id|default:"Not assigned" }}</span>
            </div>
        </div>
        
        <!-- Timeline -->
        <div class="detail-section">
            <div class="section-title">⏰ Timeline</div>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-time">{{ job.created_at|date:"M d, Y H:i:s" }}</div>
                    <div class="timeline-event">Job Created</div>
                </div>
                {% if job.started_at %}
                <div class="timeline-item">
                    <div class="timeline-time">{{ job.started_at|date:"M d, Y H:i:s" }}</div>
                    <div class="timeline-event">Processing Started</div>
                </div>
                {% endif %}
                {% if job.completed_at %}
                <div class="timeline-item">
                    <div class="timeline-time">{{ job.completed_at|date:"M d, Y H:i:s" }}</div>
                    <div class="timeline-event">
                        {% if job.status == 'completed' %}✅ Completed{% else %}❌ Failed{% endif %}
                    </div>
                </div>
                {% endif %}
                {% if job.reviewed_at %}
                <div class="timeline-item">
                    <div class="timeline-time">{{ job.reviewed_at|date:"M d, Y H:i:s" }}</div>
                    <div class="timeline-event">🔍 Reviewed by {{ job.reviewed_by }}</div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Retry Information -->
        <div class="detail-section">
            <div class="section-title">🔄 Retry Information</div>
            <div class="detail-row">
                <span class="detail-label">Retry Count:</span>
                <span class="detail-value">{{ job.retry_count }}/{{ job.max_retries }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Failure Reason:</span>
                <span class="detail-value">{{ failure_reason|default:job.failure_reason|default:"Unknown"|title }}</span>
            </div>
            {% if job.requeue_reason %}
            <div class="detail-row">
                <span class="detail-label">Requeue Reason:</span>
                <span class="detail-value">{{ job.requeue_reason }}</span>
            </div>
            {% endif %}
        </div>
        
        <!-- Order Information -->
        <div class="detail-section">
            <div class="section-title">👤 Order Information</div>
            <div class="detail-row">
                <span class="detail-label">Customer:</span>
                <span class="detail-value">{{ order.first_name }} {{ order.surname }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Email:</span>
                <span class="detail-value">{{ order.customer_email }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Order Status:</span>
                <span class="detail-value">{{ order.status|title }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Travel Date:</span>
                <span class="detail-value">{{ order.travel_date|date:"M d, Y"|default:"Not set" }}</span>
            </div>
        </div>
    </div>
    
    <!-- Error Information -->
    {% if job.error_message or job_errors or detailed_error_description %}
    <div class="detail-section error-section">
        <div class="section-title">🚨 Error Information</div>

        {% if detailed_error_description %}
        <script>
           console.log("{{ detailed_error_description|escapejs }}", 'detailed_error_description');
        </script>
        <h4>Error Details:</h4>
        <div style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; margin-bottom: 15px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
            <div style="margin: 5px 0;">{{ detailed_error_description|linebreaksbr }}</div>
        </div>
        {% elif job.error_message %}
        <h4>Latest Error Message:</h4>
        <div class="error-message">{{ job.error_message }}</div>
        {% endif %}

        {% if failure_reason %}
        <div style="margin-bottom: 15px;">
            <strong>Failure Category:</strong>
            <span style="background: #fed7d7; color: #9b2c2c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                {{ failure_reason }}
            </span>
        </div>
        {% endif %}

        {% if formatted_error_details or job.error_details %}
        <details style="margin-top: 15px;">
            <summary style="cursor: pointer; font-weight: bold; color: #666;">Technical Details (JSON)</summary>
            <div class="json-display" style="margin-top: 10px;">
                <pre>{% if formatted_error_details %}{{ formatted_error_details }}{% else %}{{ job.error_details }}{% endif %}</pre>
            </div>
        </details>
        {% endif %}

        {% if job_errors %}
        <details style="margin-top: 15px;">
            <summary style="cursor: pointer; font-weight: bold; color: #666;">Error History ({{ job_errors|length }} error{{ job_errors|length|pluralize }})</summary>
            <div style="margin-top: 10px;">
                {% for error in job_errors %}
                <div style="margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid #fc8181;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 5px;">
                        {{ error.occurred_at|date:"M d, Y H:i:s" }}
                    </div>
                    <div class="error-message">{{ error.error_message }}</div>
                </div>
                {% endfor %}
            </div>
        </details>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Review Information -->
    {% if job.status == 'review' or job.reviewed_by %}
    <div class="detail-section">
        <div class="section-title">🔍 Review Information</div>
        {% if job.reviewed_by %}
        <div class="detail-row">
            <span class="detail-label">Reviewed By:</span>
            <span class="detail-value">{{ job.reviewed_by }}</span>
        </div>
        <div class="detail-row">
            <span class="detail-label">Review Date:</span>
            <span class="detail-value">{{ job.reviewed_at|date:"M d, Y H:i:s" }}</span>
        </div>
        {% endif %}
        {% if job.review_notes %}
        <div style="margin-top: 10px;">
            <strong>Review Notes:</strong>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px;">
                {{ job.review_notes }}
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- Manual Intervention Actions -->
    <div class="detail-section">
        <div class="section-title">⚡ Manual Intervention Options</div>
        <div class="action-buttons">
            {% if job.status == 'review' or job.status == 'failed' %}
            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="action" value="requeue">
                <input type="hidden" name="reason" value="Manual requeue from job details">
                <button type="submit" class="btn btn-success" onclick="return confirm('Requeue this job for processing?')">
                    🔄 Requeue Job
                </button>
            </form>
            
            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="action" value="requeue">
                <input type="hidden" name="priority" value="true">
                <input type="hidden" name="reason" value="High priority requeue from job details">
                <button type="submit" class="btn btn-warning" onclick="return confirm('Requeue this job with HIGH PRIORITY?')">
                    ⚡ Priority Requeue
                </button>
            </form>
            {% endif %}
            
            {% if job.status != 'cancelled' %}
            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="action" value="cancel">
                <button type="submit" class="btn btn-danger" onclick="return confirm('Cancel this job? This action cannot be undone.')">
                    🚫 Cancel Job
                </button>
            </form>
            {% endif %}
            
            {% if job.status == 'failed' %}
            <form method="post" action="{% url 'queue_system:job_action' job.id %}" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="action" value="move_to_review">
                <input type="hidden" name="notes" value="Moved to review from job details page">
                <button type="submit" class="btn btn-warning" onclick="return confirm('Move this job to review queue?')">
                    🔍 Move to Review
                </button>
            </form>
            {% endif %}
            
            <a href="{% url 'admin:queue_system_queuedjob_change' job.id %}" class="btn btn-secondary">
                ✏️ Edit in Admin
            </a>
        </div>
    </div>
    
    <!-- Navigation -->
    <div style="text-align: center; margin-top: 30px;">
        <a href="javascript:history.back()" style="color: #007cba; text-decoration: none; margin-right: 20px;">
            ← Go Back
        </a>
        <a href="{% url 'queue_system:queue_overview' %}" style="color: #007cba; text-decoration: none; margin-right: 20px;">
            📊 Queue Overview
        </a>
        <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" style="color: #007cba; text-decoration: none;">
            📋 All Jobs
        </a>
    </div>
</div>
{% endblock %}
