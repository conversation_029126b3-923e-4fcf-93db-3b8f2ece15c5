# Travel Management Django

A Django-based travel management system that automates the processing of travel documentation requests.

## Project Overview

This system serves as a centralized order management dashboard that:
- Tracks and processes customer travel documentation requests
- Automates criminal background checks via the OpenSanctions API
- Stores form data for automated submission to third-party websites
- Manages QR code retrieval and customer communication
- Supports multiple travel destinations with location-specific requirements

## Core Features

- **Order Management**: Track orders from submission to completion with status tracking
- **Automated Background Checks**: Integration with OpenSanctions API to screen travelers
- **Third-Party Form Automation**: Store data for bots to populate external website forms
- **Multi-Location Support**: Handle different travel destinations with unique form requirements
- **QR Code Management**: Storage and delivery of travel documentation to customers
- **Error Handling**: Track and manage failed submission attempts

## System Architecture

- **Backend**: Django with MySQL database
- **Automation**: Python-based bots for third-party website interaction
- **API Integration**: OpenSanctions for criminal background checks

## Database Schema

Core models include:
- `Location`: Travel destination configuration
- `Order`: Customer order tracking
- `CriminalCheck`: Background check results
- `FailedSubmission`: Error tracking for automation attempts
- Location-specific form models (e.g., `BarbadosForm`)

## Getting Started

### Prerequisites
- Python 3.8+
- MySQL
- Django

### Installation
1. Clone the repository
2. Create a virtual environment
3. Install dependencies: `pip install -r requirements.txt`
4. Configure database settings
5. Run migrations: `python manage.py migrate`
6. Start the development server: `python manage.py runserver`

## License

This is a private repository. All rights reserved.




## Queue System

The queue system manages the automated processing of travel documentation requests using Celery workers.

### Starting Workers

# Start workers for all locations
python manage.py start_queue_workers

# Start workers for a specific location
python manage.py start_queue_workers --location=Barbados

# Start the scheduler worker
python manage.py start_queue_workers --scheduler

# Start Celery beat for periodic tasks
python manage.py start_queue_workers --beat

# Start everything with custom concurrency
python manage.py start_queue_workers --location=Barbados --scheduler --beat --concurrency=3

