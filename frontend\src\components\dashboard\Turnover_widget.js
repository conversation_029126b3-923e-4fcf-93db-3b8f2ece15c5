import React, { useState, useEffect } from 'react';
import { Paper, Box, Typography, FormControl, Select, MenuItem, ToggleButton, ToggleButtonGroup } from '@mui/material';

export default function TurnoverWidget({ gradient, menuBg }) {
    const [period, setPeriod] = useState('Today');
    const [grossNet, setGrossNet] = useState('net');
    const [turnover, setTurnover] = useState('...');
    const [subtitle, setSubtitle] = useState(''); // Initialize subtitle

    useEffect(() => {
        const periodMap = {
            'Today': 'today',
            'This week': 'this week',
            'This month': 'this month'
        };

        // Reset state while fetching
        setTurnover('...');
        setSubtitle('');

        fetch(`/api/turnover?period=${periodMap[period]}`)
            .then(res => res.json())
            .then(data => {
                let currentTurnover = '...';
                let percentageChange = null;

                if (grossNet === 'gross') {
                    currentTurnover = data.gross_turnover ?? '...';
                    percentageChange = data.percentage_change_gross;
                } else {
                    currentTurnover = data.net_turnover ?? '...';
                    percentageChange = data.percentage_change_net;
                }

                setTurnover(currentTurnover);

                let subtitleText = '';
                if (percentageChange !== null && percentageChange !== undefined) {
                    const sign = percentageChange >= 0 ? '+' : '';
                    if (period === 'Today') {
                        subtitleText = `${sign}${percentageChange}% from yesterday`;
                    } else if (period === 'This week') {
                        subtitleText = `${sign}${percentageChange}% from last week`;
                    } else if (period === 'This month') {
                        subtitleText = `${sign}${percentageChange}% from last month`;
                    }
                }
                setSubtitle(subtitleText);
            })
            .catch(() => {
                setTurnover('Error');
                setSubtitle('Could not load data');
            });
    }, [period, grossNet]); // Rerun effect when period or grossNet changes


    return (
        <Paper
            elevation={0}
            sx={{
                p: 3,
                borderRadius: 3,
                minWidth: 320,
                background: gradient || 'linear-gradient(-90deg, #A6DCEF 0%, #007199 100%)',
            }}
        >
            <Box display="flex" alignItems="flex-start" justifyContent="space-between"> {/* Main flex container */}
                {/* Left Section */}
                <Box>
                    {/* Turnover Label and Toggle Buttons */}
                    <Box display="flex" alignItems="center" mt={0} mb={1}>
                        <Typography variant="body2" color="white" fontWeight="bold">
                            Turnover:
                        </Typography>
                        <ToggleButtonGroup
                            value={grossNet}
                            exclusive
                            onChange={(e, val) => val && setGrossNet(val)}
                            size="small"
                            sx={{ ml: 2, height: 22 }} // reduce height of the group further
                        >
                            <ToggleButton value="gross" sx={{ color: 'white', borderColor: 'transparent', minWidth: 28, height: 22, borderRadius: 1, '&.Mui-selected': { backgroundColor: 'rgba(255, 255, 255, 0.2)', color: 'white' }, px: 0.5, py: 0, textTransform: 'none', fontSize: 12 }}>Gross</ToggleButton>
                            <ToggleButton value="net" sx={{ color: 'white', borderColor: 'transparent', minWidth: 28, height: 22, borderRadius: 1, '&.Mui-selected': { backgroundColor: 'rgba(255, 255, 255, 0.2)', color: 'white' }, px: 0.5, py: 0, textTransform: 'none', fontSize: 12 }}>Net</ToggleButton>
                        </ToggleButtonGroup>
                    </Box>

                    {/* Turnover Value */}
                    <Typography variant="h4" color="white" fontWeight="bold" sx={{ mt: 1 }}>
                        ${turnover}
                    </Typography>

                    {/* Subtitle */}
                    {subtitle && <Typography variant="body2" color="white" mt={1}>{subtitle}</Typography>}
                </Box>

                {/* Right Section (Dropdown) - Remains the same */}
                <Box>
                    <FormControl
                        size="small"
                        sx={{
                            width: 120,
                            background: 'rgba(255, 255, 255, 0.16)',
                            borderRadius: 2,
                            // Removed mt and mb, alignment handled by parent Box
                            alignSelf: 'flex-start', // Keep it aligned to the start
                        }}
                    >
                        <Select
                            value={period}
                            onChange={e => setPeriod(e.target.value)}
                            sx={{
                                color: 'white',
                                '.MuiOutlinedInput-notchedOutline': { border: 0 },
                                '& .MuiSvgIcon-root': { color: 'white' },
                                fontSize: 12,
                                pl: 1,
                            }}
                            MenuProps={{
                                PaperProps: {
                                    sx: {
                                        backgroundColor: menuBg || 'rgb(22, 120, 158)',
                                        '& .MuiMenuItem-root': {
                                            fontSize: 12,
                                            color: '#ffffff',
                                        },
                                    }
                                }
                            }}
                        >
                            <MenuItem value="Today">Today</MenuItem>
                            <MenuItem value="This week">This week</MenuItem>
                            <MenuItem value="This month">This month</MenuItem>
                        </Select>
                    </FormControl>
                </Box>
            </Box>
        </Paper>
    );
}