from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime 




# Set up Google Sheets API
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_name('credentials.json', scope)
client = gspread.authorize(creds)

# Access the spreadsheet using its ID
sheet = client.open_by_key('1x_Mw6grKUerIIZGjh2isnm2Wh55fSNyu4vzXBCyXtNM')
worksheet = sheet.worksheet('TEST AUTOMATION')  # Get the worksheet by title

# Get all values including headers
all_values = worksheet.get_all_values()

# Print the headers to see what we're dealing with
print("Current headers:")
print(all_values[0])  # First row contains headers

# Get the data with headers
headers = all_values[0]
data = all_values[1:]  # All rows except the header
df = pd.DataFrame(data, columns=headers)

# Print first few rows to verify data loading
print("\nLoaded data preview:")
print(df.head())

service = Service(executable_path="chromedriver.exe")
driver = webdriver.Chrome(service=service)

wait = WebDriverWait(driver, 10)

driver.get("https://www.travelform.gov.bb/create")






# ----------------------------------------------------------------STAGE 1: ARRIVAL----------------------------------------------------------------

# [--WHAT IS YOUR RESIDENTIAL STATUS IN BARBADOS?--]

try:
    residential_status = df.loc[0, 'What is your residential Status in Barbados']  # read data from spreadsheet
except KeyError:
    print("Column 'What is your residential Status in Barbados' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

# Click the radio button based on the residential status from spreadsheet data
time.sleep(1)

if residential_status == 'Non-Resident/Visitor':
    non_resident = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//label[span[text()='Non-Resident/Visitor']]"))
    )
    non_resident.click()
elif residential_status == 'Permanent Address Is In Barbados':
    resident = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//label[span[text()='Permanent Address Is In Barbados']]"))
    )
    resident.click()


# [--HOW ARE YOU ENTERING BARBADOS?--]

# Get the entry method from the spreadsheet
try:
    entry_method = df.loc[0, 'How are you entering Barbados?']  
except KeyError:
    print("Column 'How are you entering Barbados?' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

# Click air or sea option based on the data from spreadsheet
if entry_method == 'Air':
    air_option = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//label[.//span[text()='Air']]"))
    )
    air_option.click()
elif entry_method == 'Sea':
    sea_option = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//label[.//span[text()='Sea']]"))
    )
    sea_option.click()


# [--AIRLINE--]

time.sleep(1)

# Get the airline name from the spreadsheet
try:
    airline = df.loc[0, 'Airline']  
except KeyError:
    print("Column 'Airline' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

# Click the dropdown
try:
    dropdown_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.carrierName']"))
    )
    dropdown_element.click()
    print("Dropdown clicked")
except TimeoutException:
    print("Dropdown element not found")
    driver.quit()
    exit()

time.sleep(1)

# Type the airline name
try:                                            
    dropdown_element.send_keys(airline)
    print(f"Typed airline name: {airline}")
except Exception as e:
    print(f"Failed to type airline name: {e}")
    driver.quit()
    exit()

# Press Down followed by Enter to select the airline
try:
    dropdown_element.send_keys(Keys.DOWN, Keys. RETURN)
    print("Pressed Enter to select airline")
except Exception as e:
    print(f"Failed to press Enter: {e}")
    driver.quit()
    exit()


# [--FLIGHT REGISTRATION NUMBER--]

# Get the flight registration number from the spreadsheet
try:
    flight_registration_number = df.loc[0, 'Flight Registration Number']
except KeyError:
    print("Column 'Flight Registration Number' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

try:
    flight_registration_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.carrierID']"))
    )
    flight_registration_element.clear()
    flight_registration_element.send_keys(flight_registration_number)
    print(f"Typed flight registration number: {flight_registration_number}")
except TimeoutException:
    print("Flight registration number element not found")
    driver.quit()
    exit()


# [--COUNTRY OF EMBARKATION --]

# Get the country of embarkation from the spreadsheet
try:
    country_of_embarkation = df.loc[0, 'Country of Embarkation']
except KeyError:
    print("Column 'Country of Embarkation' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

# Click the dropdown
try:
    country_dropdown_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path=\"arrival.embarkCountry\"]"))
    )
    country_dropdown_element.click()
    print("Country dropdown clicked")
except TimeoutException:
    print("Country dropdown element not found")
    driver.quit()
    exit()

time.sleep(1)

# Type the country of embarkation
try:
    country_dropdown_element.send_keys(country_of_embarkation)
    print(f"Typed country of embarkation: {country_of_embarkation}")
except Exception as e:
    print(f"Failed to type country of embarkation: {e}")
    driver.quit()
    exit()

time.sleep(0.3)

# Press Down followed by Enter to select the country of embarkation
try:
    country_dropdown_element.send_keys(Keys.DOWN, Keys.RETURN)
    print("Pressed Enter to select country")
except Exception as e:
    print(f"Failed to press Enter: {e}")
    driver.quit()
    exit()


# [--PORT OF EMBARKATION--]

# Get the port of embarkation from the spreadsheet
try:
    port_of_embarkation = df.loc[0, 'Port of Embarkation']
except KeyError:
    print("Column 'Port of Embarkation' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

# Click the dropdown
try:
    port_dropdown_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path=\"arrival.embarkPort\"]"))
    )
    port_dropdown_element.click()
    print("Port dropdown clicked")
except TimeoutException:
    print("Port dropdown element not found")
    driver.quit()
    exit()

time.sleep(1)

# Type the port of embarkation
try:
    port_dropdown_element.send_keys(port_of_embarkation)
    print(f"Typed port of embarkation: {port_of_embarkation}")
except Exception as e:
    print(f"Failed to type port of embarkation: {e}")
    driver.quit()
    exit()

# Press Down followed by Enter to select the port
try:
    port_dropdown_element.send_keys(Keys.DOWN, Keys.RETURN)
    print("Pressed Enter to select port")
except Exception as e:
    print(f"Failed to press Enter: {e}")
    driver.quit()
    exit()


# [--INTENDED DATE OF ARRIVAL--]

# Get the intended date of arrival from the spreadsheet
try:
    expected_date_of_arrival = df.loc[0, 'Intended Date of Arrival']
except KeyError:
    print("Column 'Intended Date of Arrival' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

# Click the date input field
try:
    date_input_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path=\"arrival.travelDate\"]"))
    )
    date_input_element.click()
    print("Date input field clicked")
except TimeoutException:
    print("Date input field not found")
    driver.quit()
    exit()

time.sleep(1)

# locate the date of arrival in the calendar and parse data
try:
    date_obj = datetime.strptime(expected_date_of_arrival, '%d %B %Y')
    month_year = date_obj.strftime('%B %Y')

    day = str(date_obj.day)

    print(f"Parsed date: {day} {month_year}")

# Navigate to the correct month and year
    while True:
        current_month_year = wait.until(
            EC.visibility_of_element_located((By.XPATH, "//button[@class='mantine-focus-auto m_f6645d97 mantine-DatePickerInput-calendarHeaderLevel m_87cf2631 mantine-UnstyledButton-root']"))
        ).text
        print(f"Current month and year: {current_month_year}")
        if current_month_year == month_year:
            break
        next_button = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//button[@data-direction='next']"))
        )
        next_button.click()
        time.sleep(0.5)

# Select the correct day
    day_xpath = f"//button[@aria-label='{day} {month_year}']"
    print(f"Day XPath: {day_xpath}")
    day_button = wait.until(
        EC.element_to_be_clickable((By.XPATH, day_xpath))
    )
    day_button.click()
    print(f"Selected date: {expected_date_of_arrival}")
except Exception as e:
    print(f"Failed to select date: {e}")
    driver.quit()
    exit()


# [--NEXT STEP (STAGE 2) BUTTON--]

# Click the 'Next step' button to move to stage 2 of the application

time.sleep(1)

def click_next_step_button():
    try:
        next_step_button = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//button[@type='submit' and contains(@class, 'bg-blue-500')]"))
        )
        next_step_button.click()
        print("Clicked 'Next step' button")
    except TimeoutException:
        print("Failed to find 'Next step' button")
        driver.quit()
        exit()

# Call the function to click the 'Next step' button
click_next_step_button()





# ----------------------------------------------------------------STAGE 2: PERSONAL----------------------------------------------------------------

# [--FIRST NAME--]

# Get the first name from the spreadsheet
try:
    first_name = df.loc[0, 'First Name']
except KeyError:
    print("Column 'First Name' not found in DataFrame")
    print("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

# Input the first name into the text field
try:
    first_name_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='personal.firstName']"))
    )
    first_name_element.clear()
    first_name_element.send_keys(first_name)
    print(f"Typed first name: {first_name}")
except TimeoutException:
    print("First name element not found")
    driver.quit()
    exit()




























    













# Keep the browser open for 5 seconds after all actions
time.sleep(5)

driver.quit()

