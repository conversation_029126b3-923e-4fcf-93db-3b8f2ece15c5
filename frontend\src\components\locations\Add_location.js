import React, { useState } from 'react';
import { Container, Box, TextField, Typography, InputAdornment, Alert, CircularProgress } from '@mui/material';
// Remove <PERSON><PERSON> from MUI imports if no other MUI buttons are used
// import { Button } from '@mui/material'; 
import CustomButton from '../shared/Custom_button'; // Import CustomButton
import { useNavigate } from 'react-router-dom';

function AddLocation() {
    const [locationName, setLocationName] = useState('');
    const [description, setDescriptionField] = useState('');
    const [travellerPrice, setTravellerPrice] = useState('');
    const [costPrice, setCostPrice] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState(null);

    const navigate = useNavigate();

    const handleInputChange = (event) => {
        setLocationName(event.target.value);
    };
    const handleDescriptionChange = (event) => {
        setDescriptionField(event.target.value);
    };
    const handleTravellerPriceChange = (event) => {
        setTravellerPrice(event.target.value);
    };
    const handleCostPriceChange = (event) => {
        setCostPrice(event.target.value);
    };

    const handleSubmit = (event) => {
        event.preventDefault();
        setIsSubmitting(true);
        setSubmitError(null);
        const locationData = {
            location_name: locationName,
            description: description,
            traveller_price: travellerPrice,
            cost_price: costPrice
        };

        fetch('/api/locations/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            credentials: 'include',
            body: JSON.stringify(locationData),
        })
        .then(res => {
            if (!res.ok) {
                return res.json().then(errData => {
                    const errorDetails = Object.entries(errData).map(([key, value]) => `${key}: ${value}`).join(', ');
                    const errorMsg = `HTTP error ${res.status}: ${errorDetails || 'Unknown error'}`;
                    throw new Error(errorMsg);
                }).catch(() => {
                    throw new Error(`HTTP error! status: ${res.status}`);
                });
            }
            return res.json();
        })
        .then(data => {
            console.log('Location added successfully', data);
            setIsSubmitting(false);
            navigate('/locations', { state: { message: 'Location added successfully!' } }); // Pass message for display
        })
        .catch(error => {
            console.error('Error adding location', error);
            setSubmitError(`Failed to add location: ${error.message}`);
            setIsSubmitting(false);
        });
    };

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    return (
        <Container maxWidth="sm" sx={{ mt: 4, mb: 4 }}>
            <Typography variant="h5" component="h1" gutterBottom align="center" fontWeight={"bold"}>
                Add New Location
            </Typography>

            <Box
                component="form"
                onSubmit={handleSubmit}
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    mt: 2
                }}
                noValidate
                autoComplete="off"
            >
                {submitError && (
                    <Alert severity="error" sx={{ mb: 2 }}>{submitError}</Alert>
                )}

                <TextField
                    required
                    id="location-name"
                    label="Location name"
                    value={locationName}
                    onChange={handleInputChange}
                    variant="outlined"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    disabled={isSubmitting}
                />

                <TextField
                    required
                    id="description"
                    label="Description"
                    value={description}
                    onChange={handleDescriptionChange}
                    variant="outlined"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    multiline
                    rows={4}
                    disabled={isSubmitting}
                />

                <TextField
                    required
                    id="traveller-price"
                    label="Traveller price"
                    value={travellerPrice}
                    onChange={handleTravellerPriceChange}
                    variant="outlined"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    type="number"
                    InputProps={{
                        startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        inputProps: { min: 0, step: "0.01" }
                    }}
                    disabled={isSubmitting}
                />

                <TextField
                    required
                    id="cost-price"
                    label="Cost price"
                    value={costPrice}
                    onChange={handleCostPriceChange}
                    variant="outlined"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    type="number"
                    InputProps={{
                        startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        inputProps: { min: 0, step: "0.01" }
                    }}
                    disabled={isSubmitting}
                />

                <CustomButton // Replaced Button with CustomButton
                    type="submit"
                    variant="contained"
                    color="primary"
                    fullWidth
                    disabled={isSubmitting}
                    sx={{ mt: 3, mb: 2, p: 1.5 }} // Kept existing sx for margins and consistent padding
                >
                    {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Save Location'}
                </CustomButton>
            </Box>
        </Container>
    );
}

export default AddLocation;