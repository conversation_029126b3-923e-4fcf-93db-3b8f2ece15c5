import React from 'react';
import { Box, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Avatar, Divider, Typography } from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ChatBubbleIcon from '@mui/icons-material/ChatBubble';
import IconButton from '@mui/material/IconButton';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import { Link } from 'react-router-dom';

function SidebarMenuItem({ icon, text, open, color, showArrow, component, to }) {
  return (
    <ListItemButton
      component={component}
      to={to}
      sx={{
        my: 0.5,
        borderRadius: 2,
        mx: open ? 2 : 'auto', // match divider margin
        justifyContent: open ? 'flex-start' : 'center',
        alignItems: 'center',
        height: 48, // slightly reduced height
        transition: 'justify-content 0.3s, margin 0.3s, height 0.3s',
        position: 'relative',
        px: 0, // remove default horizontal padding
      }}
    >
      <ListItemIcon
        sx={{
          color: color,
          minWidth: open ? 36 : 0,
          justifyContent: 'center',
          alignItems: 'center',
          display: 'flex',
          width: open ? 'auto' : '100%', // ensure icon is centered when closed
          transition: 'min-width 0.3s, width 0.3s',
        }}
      >
        {icon}
      </ListItemIcon>
      <ListItemText
        primary={
          <span
            style={{
              display: 'block',
              textAlign: open ? 'left' : 'center',
              opacity: open ? 1 : 0,
              maxWidth: open ? 120 : 0,
              marginLeft: open ? 8 : 0,
              transition: 'opacity 0.3s, max-width 0.3s, margin-left 0.3s',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
            }}
          >
            {text}
          </span>
        }
        primaryTypographyProps={{ sx: { color: color, fontSize: 15, fontWeight: 500, p: 0 } }}
        sx={{ minWidth: 0, transition: 'min-width 0.3s' }}
      />
      {showArrow && open && (
        <ChevronRightIcon sx={{ position: 'absolute', right: 0, color: color, fontSize: 20 }} />
      )}
    </ListItemButton>
  );
}

export default function Sidebar({ onToggle }) {
  const [open, setOpen] = React.useState(true);
  
  const handleToggle = () => {
    const newOpen = !open;
    setOpen(newOpen);
    if (onToggle) onToggle(newOpen);
  };
  
  return (
    <Box
      sx={{
        width: open ? 260 : 60,
        transition: 'width 0.3s',
        height: '100vh',
        bgcolor: '#3D4660',
        color: '#2e488e',
        display: 'flex',
        flexDirection: 'column',
        borderRight: '1px solid #e0e0e0',
        position: 'fixed',
        left: 0,
        top: 0,
        boxShadow: 1,
      }}
    >
      {/* Logo Placeholder */}
      <Box sx={{ p: 3, display: 'flex', alignItems: 'center', justifyContent: 'center', height: 48 }}>
        {/* Replace the two old img tags with a single one for the new logo */}
        <img
          src={process.env.PUBLIC_URL + '/JGlogo.png'} // Use the new logo
          alt="Logo" // Updated alt text
          style={{
            height: open ? 40 : 35, // Adjust height based on sidebar state if needed
            maxWidth: open ? 160 : 40, // Adjust max width for open/closed states
            objectFit: 'contain',
            transition: 'height 0.3s, max-width 0.3s',
          }}
        />
        {/* The second img tag for 'JG Innovate text.png' is removed as JGlogo.png likely combines both */}
      </Box>
      
      <Divider sx={{ borderColor: '#ffffff9c', mx: open ? 2 : 'auto', width: open ? 'auto' : 32, transition: 'margin 0.3s, width 0.3s' }} />

      {/* Main Menu */}
      <Box sx={{ flex: 1 }}>
        <List>
          <SidebarMenuItem
            icon={<DashboardIcon />}
            text="Dashboard"
            open={open}
            color="#ffffff"
            showArrow={true}
            component={Link}
            to="/"
          />
          <SidebarMenuItem
            icon={<LocationOnIcon />}
            text="Locations"
            open={open}
            color="#ffffff"
            showArrow={true}
            component={Link}
            to="/locations"
          />

          <SidebarMenuItem
            icon={<WorkIcon />}
            text="Orders"
            open={open}
            color="#ffffff"
            showArrow={true}
            component={Link}
            to="/orders"
          />

          
        </List>

      <Divider sx={{ borderColor: '#ffffff9c', mx: open ? 2 : 'auto', width: open ? 'auto' : 32, transition: 'margin 0.3s, width 0.3s' }} />

      {/* Secondary Menu */}
      <List>
        <SidebarMenuItem
          icon={<ChatBubbleIcon sx={{ color: '#ffffffb5' }} />}
          text="AI Assistant"
          open={open}
          color="#ffffffb5"
          showArrow={true}
        />
      </List>

      <Box sx={{ flexGrow: 0, display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 }}>
        <IconButton onClick={handleToggle} sx={{ color: '#fff' }}>
          {open ? <ChevronLeftIcon /> : <ChevronRightIcon />}
        </IconButton>
      </Box>
      </Box>
    </Box>
  );
}