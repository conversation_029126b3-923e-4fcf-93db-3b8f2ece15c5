import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Container,
    Typography,
    TextField,
    // Button, // No longer directly needed from MUI here
    CircularProgress,
    Alert,
    Select,
    MenuItem,
    InputLabel,
    FormControl,
    Box
} from '@mui/material';
import CustomButton from '../shared/Custom_button'; // Import CustomButton

function AddOrder() {
    const navigate = useNavigate();

    const [first_name, setFirstName] = useState('');
    const [surname, setSurname] = useState('');
    const [customerEmail, setCustomerEmail] = useState('');
    const [location, setLocation] = useState('');
    const status = 'pending';
    const [qrFile, setQrFile] = useState(null);

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState(null);

    const [availableLocations, setAvailableLocations] = useState([]);
    const [loadingLocations, setLoadingLocations] = useState(true);
    const [errorLocations, setErrorLocations] = useState(null);

    useEffect(() => {
        const fetchLocations = async () => {
            setLoadingLocations(true);
            setErrorLocations(null);
            try {
                const response = await fetch('/api/locations/', {
                    credentials: 'include',
                    headers: { 'Accept': 'application/json' }
                });
                if (!response.ok) throw new Error(`Failed to fetch locations (Status: ${response.status})`);
                const data = await response.json();
                if (Array.isArray(data.results)) {
                    setAvailableLocations(data.results);
                } else if (Array.isArray(data)) {
                    setAvailableLocations(data);
                } else {
                    console.error("Received non-array data for locations:", data);
                    throw new Error("Invalid data format received for locations.");
                }
            } catch (error) {
                console.error("Error fetching locations:", error);
                setErrorLocations(error.message);
            } finally {
                setLoadingLocations(false);
            }
        };
        fetchLocations();
    }, []);

    const handleQrFileChange = (event) => {
        setQrFile(event.target.files[0]);
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        setIsSubmitting(true);
        setSubmitError(null);

        const formData = new FormData();
        formData.append('first_name', first_name);
        formData.append('surname', surname);
        formData.append('customer_email', customerEmail);
        formData.append('location', location);
        formData.append('status', status);
        if (qrFile) {
            formData.append('qr_code', qrFile);
        }

        try {
            const response = await fetch('/api/orders/', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken'),
                },
                credentials: 'include',
                body: formData,
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const detail = errorData.detail || (typeof errorData === 'string' ? errorData : `Order submission failed (Status: ${response.status})`);
                setSubmitError(detail);
                setIsSubmitting(false);
                return;
            }
            navigate('/orders', { state: { message: 'Order submitted successfully!' } });
        } catch (error) {
            console.error("Order submission error:", error);
            setSubmitError(error.message);
            setIsSubmitting(false);
        }
    };

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    return (
        <Container maxWidth="sm" sx={{ mt: 4, mb: 4 }}>
            <Typography variant="h5" component="h1" gutterBottom align="center" fontWeight={"bold"}>
                Add New Order
            </Typography>

            {submitError && <Alert severity="error" sx={{ mb: 2 }}>Error: {submitError}</Alert>}

            <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
                <TextField
                    label="First Name"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                    value={first_name}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                    disabled={isSubmitting}
                    autoFocus
                    InputLabelProps={{ shrink: true }}
                />
                <TextField
                    label="Surname"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                    value={surname}
                    onChange={(e) => setSurname(e.target.value)}
                    required
                    disabled={isSubmitting}
                    InputLabelProps={{ shrink: true }}
                />
                <TextField
                    label="Customer Email"
                    variant="outlined"
                    type="email"
                    fullWidth
                    margin="normal"
                    value={customerEmail}
                    onChange={(e) => setCustomerEmail(e.target.value)}
                    required
                    disabled={isSubmitting}
                    InputLabelProps={{ shrink: true }}
                />

                <Box sx={{ mt: 2, mb: 1 }}>
                    <CustomButton
                        variant="outlined"
                        component="label"
                        fullWidth
                        disabled={isSubmitting}
                        // sx={{ p: 1.5 }} // Assuming CustomButton default or if you want to ensure it
                    >
                        Upload QR Code
                        <input
                            type="file"
                            hidden
                            onChange={handleQrFileChange}
                            accept="image/*"
                        />
                    </CustomButton>
                    {qrFile && (
                        <Typography variant="caption" display="block" sx={{ mt: 0.5, textAlign: 'center' }}>
                            Selected file: {qrFile.name}
                        </Typography>
                    )}
                </Box>

                <FormControl fullWidth margin="normal" required disabled={isSubmitting || loadingLocations || !!errorLocations}>
                    <InputLabel id="location-select-label">Location</InputLabel>
                    <Select
                        labelId="location-select-label"
                        id="location-select"
                        value={location}
                        label="Location"
                        onChange={e => setLocation(e.target.value)}
                    >
                        <MenuItem value="" disabled>
                            <em>
                                {loadingLocations
                                    ? 'Loading locations...'
                                    : errorLocations
                                        ? `Error: ${errorLocations}`
                                        : 'Select a Location'}
                            </em>
                        </MenuItem>
                        {!loadingLocations &&
                        !errorLocations &&
                        availableLocations.map(loc => (
                            <MenuItem key={loc.id} value={loc.id}>
                                {loc.location_name}
                            </MenuItem>
                        ))}
                    </Select>
                    {errorLocations && (
                        <Typography color="error" variant="caption" sx={{ pl: 2, mt: 1 }}>
                            Could not load locations.
                        </Typography>
                    )}
                </FormControl>

                <TextField
                    label="Status"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                    value={status}
                    required
                    disabled
                    InputProps={{ readOnly: true }}
                    InputLabelProps={{ shrink: true }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3, mb: 2 }}>
                    <CustomButton
                        variant="outlined"
                        onClick={() => navigate('/orders')}
                        disabled={isSubmitting}
                        sx={{ p: 1.5 }}
                    >
                        Cancel
                    </CustomButton>
                    <CustomButton
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={isSubmitting || loadingLocations}
                        sx={{ p: 1.5 }}
                    >
                        {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Submit Order'}
                    </CustomButton>
                </Box>
            </Box>
        </Container>
    );
}

export default AddOrder;