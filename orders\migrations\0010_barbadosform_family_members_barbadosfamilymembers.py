# Generated by Django 5.1.6 on 2025-03-10 23:44

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0009_alter_barbadosform_parish"),
    ]

    operations = [
        migrations.AddField(
            model_name="barbadosform",
            name="family_members",
            field=models.CharField(
                choices=[("0", "0"), ("1", "1"), ("2", "2"), ("3", "3"), ("4", "4")],
                default="0",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="BarbadosFamilyMembers",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("first_name", models.Char<PERSON>ield(max_length=100)),
                ("surname", models.Char<PERSON><PERSON>(max_length=100)),
                (
                    "gender",
                    models.Char<PERSON><PERSON>(
                        choices=[("male", "Male"), ("female", "Female")], max_length=100
                    ),
                ),
                ("date_of_birth", models.DateField()),
                ("nationality", models.CharField(max_length=100)),
                ("travel_document_number", models.CharField(max_length=100)),
                ("travel_document_expiry", models.DateField()),
                ("visited_countries", models.TextField()),
                (
                    "farm_question",
                    models.CharField(
                        choices=[("yes", "Yes"), ("no", "No")], max_length=100
                    ),
                ),
                (
                    "barbados_form",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="barbados_family_members",
                        to="orders.barbadosform",
                    ),
                ),
            ],
        ),
    ]
