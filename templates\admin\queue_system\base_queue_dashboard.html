{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}
{{ block.super }}
<style>
  .queue-dashboard {
    margin-top: 20px;
  }
  .queue-card {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .queue-card h3 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    color: #417690;
  }
  .queue-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }
  .stat-box {
    flex: 1;
    min-width: 120px;
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
  }
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #417690;
  }
  .stat-label {
    font-size: 12px;
    color: #666;
  }
  .priority-high {
    background-color: #ffebee;
  }
  .status-failed {
    color: #d32f2f;
  }
  .status-completed {
    color: #388e3c;
  }
  .status-queued {
    color: #1976d2;
  }
  .status-processing {
    color: #f57c00;
  }
  .refresh-button {
    float: right;
    margin-top: -5px;
  }
</style>
{% endblock %}

{% block content %}
<div class="queue-dashboard">
  {% block queue_content %}{% endblock %}
</div>
{% endblock %}
