import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './Layout'; // Import the new Layout component
import Sidebar from './components/shared/Sidebar';
import { Box } from '@mui/material';
import Table from './components/locations/Locations'; // Assuming this is LocationsTable component, rename if needed
import AddLocation from './components/locations/Add_location';
import TurnoverWidget from './components/dashboard/Turnover_widget'; // Corrected import
import { MiniStatWidget } from './components/dashboard/Orders_widget'; // Corrected import
import EditLocation from './components/locations/Edit_location'; // Import the EditLocation component
import OrdersTable from './components/orders/Orders'; // Import the OrdersTable component
import AddOrder from './components/orders/AddOrder'; // Import the AddOrder component
import EditOrder from './components/orders/EditOrder';

// Helper function (keep as is)
function getSubtitle(period, value, label) {
  if (value === null || value === undefined) {
    return `N/A from ${label}`;
  }
  const sign = value > 0 ? '+ ' : value < 0 ? '- ' : '';
  return `${sign}${Math.round(value)}% from ${label}`;
}

// Dashboard Component (keep as is for now, could be refactored later)
function Dashboard({ sidebarOpen, handleSidebarToggle }) {
  const [totalOrders, setTotalOrders] = useState('...');
  const [grossNet, setGrossNet] = useState('gross');
  const [turnover, setTurnover] = useState('...');
  const [percentageChangeDay, setPercentageChangeDay] = useState(null);
  const [percentageChangeWeek, setPercentageChangeWeek] = useState(null);
  const [percentageChangeMonth, setPercentageChangeMonth] = useState(null);
  const [ordersPeriod, setOrdersPeriod] = useState('Today');

  // API call for total orders
  useEffect(() => {
    const periodMap = {
      'Today': 'today',
      'This week': 'this week',
      'This month': 'this month'
    };

    fetch(`/api/total-orders?period=${periodMap[ordersPeriod]}`)
      .then(res => res.json())
      .then(data => {
        setTotalOrders(data.total_orders);
        setPercentageChangeDay(data.percentage_change_day);
        setPercentageChangeWeek(data.percentage_change_week);
        setPercentageChangeMonth(data.percentage_change_month);
      })
      .catch(() => {
        setTotalOrders('Error');
        setPercentageChangeDay(null);
        setPercentageChangeWeek(null);
        setPercentageChangeMonth(null);
      });
  }, [ordersPeriod]);

  // API call for turnover
  useEffect(() => {
    const periodMap = {
      'Today': 'today',
      'This week': 'this week',
      'This month': 'this month'
    };

    fetch(`/api/turnover?period=${periodMap[ordersPeriod]}&type=${grossNet}`)
      .then(res => res.json())
      .then(data => {
        setTurnover(data.turnover);
        // Optionally set percentage change for turnover if your API returns it
      })
      .catch(() => {
        setTurnover('Error');
      });
  }, [ordersPeriod, grossNet]);

  // Widget logic (keep as is)
  let subtitle = '';
  if (ordersPeriod === 'Today') {
    if (percentageChangeDay === null || percentageChangeDay === undefined) {
      subtitle = 'N/A from yesterday';
    } else {
      const sign = percentageChangeDay > 0 ? '+ ' : percentageChangeDay < 0 ? '- ' : '';
      subtitle = `${sign}${Math.abs(Math.round(percentageChangeDay))}% from yesterday`;
    }
  } else if (ordersPeriod === 'This week') {
    if (percentageChangeWeek === null || percentageChangeWeek === undefined) {
      subtitle = 'N/A from last week';
    } else {
      const sign = percentageChangeWeek > 0 ? '+ ' : percentageChangeWeek < 0 ? '- ' : '';
      subtitle = `${sign}${Math.abs(Math.round(percentageChangeWeek))}% from last week`;
    }
  } else if (ordersPeriod === 'This month') {
    if (percentageChangeMonth === null || percentageChangeMonth === undefined) {
      subtitle = 'N/A from last month';
    } else {
      const sign = percentageChangeMonth > 0 ? '+ ' : percentageChangeMonth < 0 ? '- ' : '';
      subtitle = `${sign}${Math.abs(Math.round(percentageChangeMonth))}% from last month`;
    }
  } else {
    subtitle = '';
  }

  return (
    // Dashboard still uses its own layout structure for now
    <div style={{ display: 'flex' }}>
      <Sidebar onToggle={handleSidebarToggle} />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          ml: sidebarOpen ? '260px' : '60px',
          transition: 'margin-left 0.3s'
        }}
      >
        {/* Dashboard content */}
        <Box sx={{ maxWidth: 500, ml: '0' }}>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 3, mt: 2 }}>
            <TurnoverWidget
              gradient="linear-gradient(-90deg, #A6DCEF 0%, #007199 100%)"
              menuBg="rgb(22, 120, 158)"
            />
            <MiniStatWidget
              title="Total Orders:"
              apiEndpoint="/api/total-orders"
              gradient="linear-gradient(-90deg, #FFD166 0%, #D79600 100%)"
              menuBg="rgb(220, 164, 33)"
            />
            <MiniStatWidget
              title="Average Order Value:"
              apiEndpoint="/api/average-order-value"
              gradient="linear-gradient(-90deg, #6EE7B7 0%, #00A262 100%)"
              menuBg="rgb(0, 162, 98)"
              prefix="$"
            />
            <MiniStatWidget
              title="Failed Bot Submissions:"
              apiEndpoint="/api/failed-submissions"
              gradient="linear-gradient(-90deg, #FF5A5F 0%, #A7255D 100%)"
              menuBg="rgb(167, 37, 93)"
            />
          </Box>
        </Box>
      </Box>
    </div>
  );
}

// Main App Component using Layout for specific routes
function App() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const handleSidebarToggle = (isOpen) => {
    setSidebarOpen(isOpen);
  };

  return (
    // Routes component defines the different pages
    <Routes>
      {/* Dashboard Route - uses its own layout structure */}
      <Route
        path="/"
        element={<Dashboard sidebarOpen={sidebarOpen} handleSidebarToggle={handleSidebarToggle} />}
      />

      {/* Locations Route - uses the shared Layout component */}
      <Route path="/locations" element={
        <Layout sidebarOpen={sidebarOpen} handleSidebarToggle={handleSidebarToggle}>
          <Table /> {/* Page content is passed as children */}
        </Layout>
      } />

      {/* Add Location Route - uses the shared Layout component */}
      <Route path="/locations/add" element={
        <Layout sidebarOpen={sidebarOpen} handleSidebarToggle={handleSidebarToggle}>
          <AddLocation /> {/* Page content is passed as children */}
        </Layout>
      } />

      {/* Edit Location Route - uses the shared Layout component */}
      <Route path="/locations/:id/edit" element={
        <Layout sidebarOpen={sidebarOpen} handleSidebarToggle={handleSidebarToggle}>
          <EditLocation /> {/* Page content is passed as children */}
        </Layout>
      } />

      {/* Orders Route - uses the shared Layout component */}
      <Route path="/orders" element={
        <Layout sidebarOpen={sidebarOpen} handleSidebarToggle={handleSidebarToggle}>
          <OrdersTable />
        </Layout>
      } />

      {/* Add Order Route - uses the shared Layout component */}
      <Route path="/orders/add" element={
        <Layout sidebarOpen={sidebarOpen} handleSidebarToggle={handleSidebarToggle}>
          <AddOrder />
        </Layout>
      } />

      {/* Add other routes here using the Layout component as needed */}
      <Route path="/orders/:id/edit" element={<EditOrder />} />

    </Routes>
  );
}

export default App;