from django.core.management.base import BaseCommand
from locations.models import Location
import subprocess
import time
import signal
import sys
import redis

class Command(BaseCommand):
    help = 'Start Celery workers for each location queue (like your celery.py config)'

    def add_arguments(self, parser):
        parser.add_argument('--workers-per-location', type=int, default=1, help='Number of workers per location (default: 1)')
        parser.add_argument('--concurrency', type=int, default=1, help='Celery worker concurrency (default: 1)')
        parser.add_argument('--beat', action='store_true', help='Start Celery beat scheduler')
        parser.add_argument('--location', type=str, help='Start workers for specific location only')

    def check_redis_connection(self):
        """Check if Redis is available"""
        try:
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            return True
        except Exception as e:
            self.stdout.write(f"  ❌ Redis connection failed: {str(e)}")
            return False

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("🚀 STARTING CELERY WORKERS FOR LOCATION QUEUES"))
        self.stdout.write("=" * 70)
        self.stdout.write("This starts Celery workers for location-specific queues")
        self.stdout.write("Each location gets its own queue: location.<location_id>")
        
        # Check Redis connection first
        self.stdout.write(f"\n🔍 CHECKING REDIS CONNECTION...")
        redis_available = self.check_redis_connection()
        
        if not redis_available:
            self.stdout.write(self.style.ERROR("❌ Redis is required for Celery workers"))
            self.stdout.write("💡 Start Redis server or use: python manage.py start_all_location_workers")
            return
        else:
            self.stdout.write(self.style.SUCCESS("✅ Redis connection successful"))
        
        # Get locations
        if options['location']:
            locations = Location.objects.filter(location_name__icontains=options['location'])
        else:
            locations = Location.objects.all()
        
        if not locations.exists():
            self.stdout.write(self.style.ERROR("❌ No locations found"))
            return
        
        workers_per_location = options['workers_per_location']
        concurrency = options['concurrency']
        start_beat = options['beat']
        
        self.stdout.write(f"\n📍 Found {locations.count()} locations")
        self.stdout.write(f"👥 Starting {workers_per_location} Celery worker(s) per location")
        self.stdout.write(f"🔧 Worker concurrency: {concurrency}")
        if start_beat:
            self.stdout.write(f"📅 Celery Beat: Enabled")
        self.stdout.write("=" * 70)
        
        workers_started = []
        
        # Start Celery workers for each location
        for location in locations:
            queue_name = f'location.{location.id}'
            self.stdout.write(f"\n📍 Starting workers for: {location.location_name}")
            self.stdout.write(f"   Queue: {queue_name}")
            
            for worker_num in range(workers_per_location):
                worker_name = f"celery_worker_{location.location_name}_{worker_num + 1}"
                
                self.stdout.write(f"  → Starting {worker_name}")
                
                # Build Celery worker command
                cmd = [
                    'celery', '-A', 'config', 'worker',
                    '--loglevel=info',
                    f'--concurrency={concurrency}',
                    f'--queues={queue_name}',
                    f'--hostname={worker_name}@%h',
                    '--without-gossip',
                    '--without-mingle',
                    '--without-heartbeat'
                ]
                
                try:
                    # Start the worker process
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        universal_newlines=True
                    )
                    
                    workers_started.append({
                        'name': worker_name,
                        'location': location.location_name,
                        'location_id': location.id,
                        'queue': queue_name,
                        'pid': process.pid,
                        'process': process,
                        'cmd': cmd
                    })
                    
                    self.stdout.write(f"    ✅ {worker_name} started (PID: {process.pid})")
                    
                    # Small delay between worker starts
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"    ❌ Failed to start {worker_name}: {e}"))
        
        # Start Celery Beat if requested
        if start_beat:
            self.stdout.write(f"\n📅 Starting Celery Beat scheduler...")
            
            beat_cmd = [
                'celery', '-A', 'config', 'beat',
                '--loglevel=info',
                '--scheduler=django_celery_beat.schedulers:DatabaseScheduler'
            ]
            
            try:
                beat_process = subprocess.Popen(
                    beat_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                workers_started.append({
                    'name': 'celery_beat',
                    'location': 'scheduler',
                    'location_id': 'scheduler',
                    'queue': 'beat',
                    'pid': beat_process.pid,
                    'process': beat_process,
                    'cmd': beat_cmd
                })
                
                self.stdout.write(f"  ✅ Celery Beat started (PID: {beat_process.pid})")
                
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"  ❌ Celery Beat failed: {e}"))
        
        # Summary
        self.stdout.write(f"\n" + "="*70)
        self.stdout.write(self.style.SUCCESS(f"🎉 CELERY WORKERS STARTED FOR ALL LOCATIONS"))
        self.stdout.write(f"Total workers started: {len(workers_started)}")
        
        # Group by location for display
        location_workers = {}
        for worker in workers_started:
            loc = worker['location']
            if loc not in location_workers:
                location_workers[loc] = []
            location_workers[loc].append(worker)
        
        for location, workers in location_workers.items():
            self.stdout.write(f"\n📍 {location}:")
            for worker in workers:
                queue_info = f" (Queue: {worker['queue']})" if 'queue' in worker else ""
                self.stdout.write(f"  🔴 {worker['name']} (PID: {worker['pid']}){queue_info}")
        
        self.stdout.write(f"\n📋 How it works:")
        self.stdout.write(f"  • Each location has its own Celery queue: location.<location_id>")
        self.stdout.write(f"  • Workers process jobs from their assigned location queue")
        self.stdout.write(f"  • Jobs are routed to queues based on location (see config/celery.py)")
        self.stdout.write(f"  • Redis is used as the message broker")
        
        self.stdout.write(f"\n🔍 Monitoring:")
        self.stdout.write(f"  • Check processes: tasklist /FI \"IMAGENAME eq celery.exe\"")
        self.stdout.write(f"  • Check Redis queues: redis-cli LLEN location.<location_id>")
        self.stdout.write(f"  • Celery monitor: celery -A config flower")
        self.stdout.write(f"  • Stop all workers: taskkill /F /IM celery.exe")
        
        # Set up signal handler for graceful shutdown
        def signal_handler(sig, frame):
            self.stdout.write(f"\n\n🛑 STOPPING ALL CELERY WORKERS...")
            for worker in workers_started:
                try:
                    worker['process'].terminate()
                    self.stdout.write(f"  ✅ Stopped {worker['name']} (PID: {worker['pid']})")
                except:
                    pass
            self.stdout.write(f"✅ All workers stopped")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        self.stdout.write("="*70)
        self.stdout.write(f"\n👀 MONITORING CELERY WORKERS (Press Ctrl+C to stop all)")
        self.stdout.write("-" * 50)
        
        # Monitor workers and restart if needed
        try:
            check_count = 0
            while True:
                check_count += 1
                time.sleep(30)  # Check every 30 seconds
                
                self.stdout.write(f"\n[{time.strftime('%H:%M:%S')}] Check #{check_count}: Monitoring {len(workers_started)} workers...")
                
                running_count = 0
                stopped_workers = []
                
                for worker in workers_started:
                    if worker['process'].poll() is None:
                        running_count += 1
                    else:
                        stopped_workers.append(worker)
                
                if stopped_workers:
                    self.stdout.write(f"⚠️  {len(stopped_workers)} worker(s) stopped - restarting...")
                    
                    for worker in stopped_workers:
                        self.stdout.write(f"  🔄 Restarting {worker['name']} for {worker['location']}")
                        
                        try:
                            # Restart the worker
                            new_process = subprocess.Popen(
                                worker['cmd'],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                universal_newlines=True
                            )
                            worker['process'] = new_process
                            worker['pid'] = new_process.pid
                            self.stdout.write(f"    ✅ Restarted {worker['name']} (PID: {new_process.pid})")
                            running_count += 1
                        except Exception as e:
                            self.stdout.write(f"    ❌ Failed to restart {worker['name']}: {e}")
                
                if running_count == len(workers_started):
                    self.stdout.write(f"✅ All {running_count} Celery workers running")
                elif running_count > 0:
                    self.stdout.write(f"⚠️  {running_count}/{len(workers_started)} workers running")
                else:
                    self.stdout.write(self.style.ERROR("❌ No workers running - attempting restart..."))
                    
        except KeyboardInterrupt:
            self.stdout.write(f"\n\n🛑 Shutdown requested by user")
            signal_handler(None, None)
