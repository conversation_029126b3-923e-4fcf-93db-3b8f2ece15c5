# Generated by Django 5.1.6 on 2025-02-13 12:59

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0003_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CriminalCheckDatabase",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Criminal Check Database",
                "verbose_name_plural": "Criminal Check Databases",
            },
        ),
        migrations.CreateModel(
            name="FailedSubmission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("error_message", models.TextField()),
                ("retry_attempts", models.IntegerField(default=0)),
                ("last_attempt", models.DateTimeField(auto_now=True)),
                (
                    "order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="failed_submissions",
                        to="orders.order",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CriminalCheck",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "check_status",
                    models.CharField(
                        choices=[
                            ("bot_failed", "Bot was unable to complete the check"),
                            ("passed", "Passed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("checked_at", models.DateTimeField(auto_now_add=True)),
                (
                    "order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="orders.order"
                    ),
                ),
                (
                    "database",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="orders.criminalcheckdatabase",
                    ),
                ),
            ],
            options={
                "unique_together": {("order", "database")},
            },
        ),
    ]
