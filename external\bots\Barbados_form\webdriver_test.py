from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_webdriver():
    try:
        # Set the path to the ChromeDriver executable
        service = Service(executable_path="chromedriver.exe")

        # Initialize the Chrome WebDriver
        driver = webdriver.Chrome(service=service)

        # Navigate to a website
        driver.get("https://www.google.com")
        logging.info("Navigated to Google")

        # Get the title of the page
        title = driver.title
        logging.info(f"Page title: {title}")

        # Check if the title contains "Google"
        if "Google" in title:
            logging.info("WebDriver test successful!")
        else:
            logging.error("WebDriver test failed: Incorrect title")

    except Exception as e:
        logging.error(f"WebDriver test failed: {e}")
    finally:
        # Close the WebDriver
        if 'driver' in locals():
            driver.quit()
            logging.info("WebDriver closed")

# Run the test
test_webdriver()