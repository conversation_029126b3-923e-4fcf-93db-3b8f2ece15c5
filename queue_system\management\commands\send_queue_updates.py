from django.core.management.base import BaseCommand
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from queue_system.models import LocationQueueConfig, QueuedJob
from django.utils import timezone

class Command(BaseCommand):
    help = 'Sends queue updates to WebSocket clients'

    def handle(self, *args, **options):
        channel_layer = get_channel_layer()
        
        locations = LocationQueueConfig.objects.select_related('location').all()
        
        overview = {
            'total_active_jobs': QueuedJob.objects.filter(status='processing').count(),
            'total_waiting_jobs': QueuedJob.objects.filter(status='queued').count(),
            'total_active_workers': sum(loc.active_workers for loc in locations),
            'total_max_workers': sum(loc.max_workers for loc in locations),
            'locations': []
        }
        
        for loc in locations:
            jobs = QueuedJob.objects.filter(location=loc.location)
            completed = jobs.filter(status='completed').count()
            failed = jobs.filter(status='failed').count()
            total = completed + failed
            
            overview['locations'].append({
                'id': str(loc.location.id),
                'active_workers': loc.active_workers,
                'max_workers': loc.max_workers,
                'queued': jobs.filter(status='queued').count(),
                'processing': jobs.filter(status='processing').count(),
                'success_rate': round((completed / total) * 100, 1) if total > 0 else 100,
            })
        
        async_to_sync(channel_layer.group_send)(
            "queue_updates",
            {
                "type": "queue.update",
                "data": {
                    "type": "queue_update",
                    "data": overview
                }
            }
        )