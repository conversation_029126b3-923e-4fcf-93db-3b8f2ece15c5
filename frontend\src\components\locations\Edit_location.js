import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
    Box,
    TextField,
    Typography,
    InputAdornment,
    Alert,
    Container,
    // Button, // No longer directly needed from MUI here
    CircularProgress
} from '@mui/material';
import Sidebar from '../shared/Sidebar';
import CustomButton from '../shared/Custom_button'; // Import CustomButton

// Function to get CSRF token (same as in EditOrder.js)
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function EditLocation() {
    const [locationName, setLocationName] = useState('');
    const [description, setDescriptionField] = useState('');
    const [travellerPrice, setTravellerPrice] = useState('');
    const [costPrice, setCostPrice] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState(null);
    const [loading, setLoading] = useState(true);

    const navigate = useNavigate();
    const { id } = useParams();

    useEffect(() => {
        fetch(`/api/locations/${id}/`, {
            credentials: 'include',
            headers: { 'Accept': 'application/json' }
        })
            .then(res => {
                if (!res.ok) {
                    return res.json().then(errData => {
                        throw new Error(errData.detail || `Failed to fetch location (Status: ${res.status})`);
                    }).catch(() => {
                        throw new Error(`Failed to fetch location (Status: ${res.status})`);
                    });
                }
                return res.json();
            })
            .then(data => {
                setLocationName(data.location_name || '');
                setDescriptionField(data.description || '');
                setTravellerPrice(data.traveller_price || '');
                setCostPrice(data.cost_price || '');
            })
            .catch(error => {
                setSubmitError('Failed to load location: ' + error.message);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [id]);

    const handleSubmit = (event) => {
        event.preventDefault();
        setIsSubmitting(true);
        setSubmitError(null);
        const locationData = {
            location_name: locationName,
            description: description,
            traveller_price: travellerPrice,
            cost_price: costPrice
        };
        fetch(`/api/locations/${id}/`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            credentials: 'include',
            body: JSON.stringify(locationData),
        })
        .then(res => {
            if (!res.ok) {
                return res.json().then(errData => {
                    const errorMsg = errData.detail || JSON.stringify(errData) || `HTTP error ${res.status}`;
                    throw new Error(errorMsg);
                }).catch(() => {
                    throw new Error(`HTTP error! status: ${res.status}`);
                });
            }
            if (res.status === 204) {
                return null;
            }
            return res.json();
        })
        .then(data => {
            navigate('/locations', { state: { message: 'Location updated successfully!' } });
        })
        .catch(error => {
            setSubmitError(`Failed to update location: ${error.message}`);
        })
        .finally(() => {
            setIsSubmitting(false);
        });
    };

    if (loading) {
        return (
            <Box sx={{ display: 'flex' }}>
                <Sidebar />
                <Container maxWidth="sm" sx={{ mt: 4, mb: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', flexGrow: 1 }}>
                    <CircularProgress />
                </Container>
            </Box>
        );
    }

    return (
        <Box sx={{ display: 'flex' }}>
            <Sidebar />
            <Container maxWidth="sm" sx={{ mt: 4, mb: 4, flexGrow: 1 }}>
                <Typography variant="h5" component="h1" gutterBottom align="center" fontWeight="bold">
                    Edit Location
                </Typography>
                {submitError && (
                    <Alert severity="error" sx={{ mb: 2 }}>{submitError}</Alert>
                )}
                <Box component="form" onSubmit={handleSubmit} noValidate autoComplete="off">
                    <TextField
                        required
                        id="location-name"
                        label="Location Name"
                        value={locationName}
                        onChange={e => setLocationName(e.target.value)}
                        variant="outlined"
                        fullWidth
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />
                    <TextField
                        required
                        id="description"
                        label="Description"
                        value={description}
                        onChange={e => setDescriptionField(e.target.value)}
                        variant="outlined"
                        fullWidth
                        multiline
                        rows={4}
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />
                    <TextField
                        required
                        id="traveller-price"
                        label="Traveller Price"
                        value={travellerPrice}
                        onChange={e => setTravellerPrice(e.target.value)}
                        variant="outlined"
                        fullWidth
                        type="number"
                        InputProps={{
                            startAdornment: <InputAdornment position="start">$</InputAdornment>,
                            inputProps: { min: 0, step: "0.01" }
                        }}
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />
                    <TextField
                        required
                        id="cost-price"
                        label="Cost Price"
                        value={costPrice}
                        onChange={e => setCostPrice(e.target.value)}
                        variant="outlined"
                        fullWidth
                        type="number"
                        InputProps={{
                            startAdornment: <InputAdornment position="start">$</InputAdornment>,
                            inputProps: { min: 0, step: "0.01" }
                        }}
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}> {/* Added mt: 2 for spacing */}
                        <CustomButton
                            variant="outlined"
                            onClick={() => navigate('/locations')}
                            disabled={isSubmitting}
                            sx={{ p: 1.5 }} // Consistent padding
                        >
                            Cancel
                        </CustomButton>
                        <CustomButton
                            variant="contained"
                            type="submit"
                            disabled={isSubmitting}
                            sx={{ p: 1.5 }} // Consistent padding
                        >
                            {isSubmitting ? <CircularProgress size={24} /> : 'Save Changes'}
                        </CustomButton>
                    </Box>
                </Box>
            </Container>
        </Box>
    );
}

export default EditLocation;