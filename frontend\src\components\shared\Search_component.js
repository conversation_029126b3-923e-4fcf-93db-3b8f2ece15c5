import React, { useState } from 'react';
import { Box, TextField } from '@mui/material';
import CustomButton from './Custom_button';
import SearchIcon from '@mui/icons-material/Search';

// Removed the incorrect default value for onSearchSubmit
function SearchComponent({ onSearchSubmit, placeholder = "Search..." }) {
    const [searchTerm, setSearchTerm] = useState('');

    const handleInputChange = (event) => {
        setSearchTerm(event.target.value);
    };

    const handleSubmit = () => {
        // Check if the prop was actually passed before calling it
        if (onSearchSubmit) {
            onSearchSubmit(searchTerm);
        }
    };

    const handleKeyPress = (event) => {
        if (event.key === 'Enter') {
            handleSubmit();
        }
    };

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <TextField
                label={placeholder}
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                sx={{ flexGrow: 1 }}
            />
            <CustomButton
                variant="contained"
                onClick={handleSubmit}
                startIcon={<SearchIcon />}
                sx= {{ p: 1.5 }}
            >
                Search
            </CustomButton>
        </Box>
    );
}

export default SearchComponent;