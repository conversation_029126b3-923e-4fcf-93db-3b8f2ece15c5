# Travel Management System - Queue System Specification

## Overview

This document outlines the technical specification for implementing a queuing system for the Travel Management Django application. The system will manage automated form submission bots for multiple travel destinations, with support for location-specific processing rules, worker scaling, and failure handling.

## Core Requirements

1. **Location-Specific Queues**: Separate processing queues for each travel destination
2. **Time Window Support**: Handle locations with and without time-based processing restrictions
3. **Scalable Workers**: Support multiple bot instances per location for high-volume destinations
4. **Failure Management**: Review and correction workflow for failed submissions
5. **Monitoring Dashboard**: Visualize queue status and performance metrics

## System Architecture

### Components

1. **Queue Manager**:
   - Maintains separate queues for each location
   - Handles job scheduling based on location-specific rules
   - Manages job prioritization within each queue

2. **Worker Manager**:
   - Launches and monitors bot instances
   - Assigns jobs to appropriate bot workers
   - Tracks worker status and performance

3. **Admin Dashboard**:
   - Visualizes queue status and metrics
   - Allows manual intervention (reprioritize, retry, cancel)
   - Provides historical performance data

### Data Model

```python
class LocationQueueConfig(models.Model):
    location = models.OneToOneField(Location, on_delete=models.CASCADE)
    has_time_window = models.BooleanField(default=False)
    window_days_before_travel = models.PositiveIntegerField(null=True, blank=True)
    max_workers = models.PositiveIntegerField(default=1)
    active_workers = models.PositiveIntegerField(default=0)
    priority_level = models.PositiveIntegerField(default=1)  # For location-level priority

class QueuedJob(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    order = models.ForeignKey('orders.Order', on_delete=models.CASCADE)
    location = models.ForeignKey('locations.Location', on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=[
        ('waiting', 'Waiting for processing window'),
        ('queued', 'In active queue'),
        ('processing', 'Currently processing'),
        ('completed', 'Successfully completed'),
        ('failed', 'Failed - needs review'),
        ('cancelled', 'Cancelled')
    ])
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_for = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    priority_flag = models.BooleanField(default=False)
    worker_id = models.CharField(max_length=100, null=True, blank=True)
    
class JobError(models.Model):
    job = models.ForeignKey(QueuedJob, on_delete=models.CASCADE, related_name='errors')
    error_message = models.TextField()
    error_trace = models.TextField(null=True, blank=True)
    occurred_at = models.DateTimeField(auto_now_add=True)
```

## Queue Types

Each location will have these logical queues:

1. **Active Queue**: 
   - For immediate processing
   - Contains all eligible jobs
   - Sorted by priority and creation date

2. **Waiting Queue** (only for locations with time windows):
   - For jobs outside their processing window
   - Automatically moved to active queue when eligible
   - Sorted by scheduled processing date

3. **Review Queue**:
   - For failed jobs requiring manual intervention
   - Common across all locations or separated by location

## Processing Flow

### Job Creation

1. When an order passes criminal check, create a queued job
2. Check location configuration for time window requirements
3. If within processing window (or no window), add to active queue
4. If outside window, add to waiting queue with scheduled date

```python
def schedule_job(order, location_config):
    # Calculate travel date from order
    travel_date = get_travel_date(order)
    
    # Create job record
    job = QueuedJob.objects.create(
        order=order,
        location=location_config.location
    )
    
    # Check if location has time window
    if location_config.has_time_window:
        # Calculate window open date
        window_open_date = travel_date - timedelta(days=location_config.window_days_before_travel)
        
        # Check if we're in the window
        if datetime.now() >= window_open_date:
            # In window - add to active queue
            job.status = 'queued'
            job.save()
            add_to_active_queue(job)
        else:
            # Outside window - add to waiting queue with scheduled time
            job.status = 'waiting'
            job.scheduled_for = window_open_date
            job.save()
            add_to_waiting_queue(job)
    else:
        # No time window - add directly to active queue
        job.status = 'queued'
        job.save()
        add_to_active_queue(job)
```

### Job Processing

1. Worker pulls highest priority job from active queue
2. Marks job as "processing"
3. Executes bot logic for the specific location
4. Updates job status on completion/failure

### Failure Handling

1. Job fails after max retry attempts or specific error types
2. System moves job to review queue
3. Records detailed error information
4. Admin reviews and corrects order data
5. Admin requeues job (with or without priority)

## Worker Scaling

### Configuration

- Each location has a `max_workers` setting
- System maintains count of `active_workers`
- Admin can adjust worker count through interface

### Worker Management

- Workers register themselves when starting
- Heartbeat mechanism to detect crashed workers
- Automatic job reassignment if a worker crashes
- Workers are location-specific (e.g., Barbados workers only process Barbados jobs)

### Scaling Process

1. Start with one worker per location
2. Monitor queue length and processing times
3. Increase worker count for locations with growing backlogs
4. Reduce worker count when queues are consistently empty

## Technical Implementation

### Recommended Stack

**Django + Celery + Redis**:

1. **Celery** for task queue management:
   - Robust task queue management
   - Worker scaling capabilities
   - Priority queues
   - Scheduled tasks
   - Task routing to specific workers

2. **Redis** for queue backend:
   - Fast in-memory queue storage
   - Pub/sub for worker communication
   - Atomic operations for locking
   - Persistence for reliability

### Implementation Details

1. **Queue Configuration**:
   ```python
   # Celery routing configuration
   task_routes = {
       'process_order': {'queue': 'location.{location_id}'},
       'check_waiting_queue': {'queue': 'scheduler'},
       'process_failed_job': {'queue': 'review'}
   }
   ```

2. **Worker Assignment**:
   ```python
   # Worker startup command
   celery -A project worker -Q location.barbados -c 2
   ```
   This starts 2 workers dedicated to the Barbados queue.

3. **Job Scheduling**:
   ```python
   # For locations with time windows, schedule for future processing
   if not in_window:
       process_order.apply_async(
           args=[order_id], 
           eta=window_open_date,
           queue=f'location.{location_id}'
       )
   else:
       # Process immediately
       process_order.apply_async(
           args=[order_id], 
           queue=f'location.{location_id}'
       )
   ```

## Admin Interface

### Dashboard Views

1. **Queue Overview**:
   - Active queue length per location
   - Waiting queue length (if applicable)
   - Active workers per location
   - Success/failure rates
   - Recent job completions/failures

2. **Location Queue Details**:
   - Jobs in active queue with status and age
   - Jobs in waiting queue with scheduled date
   - Worker status for this location
   - Controls to adjust worker count

3. **Review Queue**:
   - Failed jobs across all locations
   - Filterable by location
   - Error details and correction interface
   - Options to requeue, prioritize, or cancel jobs

4. **Job Details**:
   - Complete job history
   - All associated errors
   - Order data
   - Manual intervention options

## Implementation Phases

### Phase 1: Core Infrastructure

1. Set up Celery with Redis backend
2. Implement basic queue models and logic
3. Create single-worker bot processing
4. Develop basic monitoring dashboard

### Phase 2: Advanced Features

1. Implement time window scheduling
2. Add review queue and correction workflow
3. Develop worker scaling capabilities
4. Enhance monitoring and reporting

### Phase 3: Optimization

1. Add performance metrics and analytics
2. Implement automatic scaling based on queue metrics
3. Add alerting for queue backlogs or high failure rates
4. Optimize resource usage and job prioritization

## Conclusion

This queuing system provides a scalable, flexible solution for managing automated form submissions across multiple travel destinations. It supports varying processing rules per location, allows for horizontal scaling as volume increases, and includes robust error handling and monitoring capabilities.