from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from orders.models import order
from .tasks import schedule_job
from .models import QueuedJob
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()

@receiver(post_save, sender=order)
def create_queue_job_on_criminal_check_passed(sender, instance, created, **kwargs):
    """
    When an order's status changes to 'criminal_check_passed',
    create a queued job for it.
    """
    logger.info(f"Signal received for order {instance.id} with status: {instance.status}")
    
    if instance.status == 'criminal_check_passed':
        logger.info(f"Order {instance.id} passed criminal check, creating queue job")
        try:
            # Use apply instead of delay for synchronous execution during debugging
            job_id = schedule_job(str(instance.id))
            logger.info(f"Job created with ID: {job_id}")
            return job_id
        except Exception as e:
            logger.error(f"Failed to schedule job for order {instance.id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    else:
        logger.debug(f"Order status {instance.status} does not trigger job creation")


def send_admin_live_update():
    """Send live update to admin WebSocket clients"""
    if channel_layer:
        try:
            # Get queue statistics
            total_jobs = QueuedJob.objects.count()
            queued_jobs = QueuedJob.objects.filter(status='queued').count()
            processing_jobs = QueuedJob.objects.filter(status='processing').count()
            completed_jobs = QueuedJob.objects.filter(status='completed').count()
            failed_jobs = QueuedJob.objects.filter(status='failed').count()
            review_jobs = QueuedJob.objects.filter(status='review').count()

            # Get recent jobs (last 10)
            recent_jobs = list(QueuedJob.objects.select_related('order', 'location')
                              .order_by('-created_at')[:10]
                              .values(
                                  'id', 'status', 'retry_count', 'max_retries',
                                  'created_at', 'failure_reason',
                                  'order__first_name', 'order__surname',
                                  'location__location_name'
                              ))

            # Convert datetime objects to strings for JSON serialization
            from django.utils import timezone
            for job in recent_jobs:
                if job['created_at']:
                    job['created_at'] = job['created_at'].isoformat()

            update_data = {
                'type': 'admin_update',
                'data': {
                    'overview': {
                        'total_jobs': total_jobs,
                        'queued': queued_jobs,
                        'processing': processing_jobs,
                        'completed': completed_jobs,
                        'failed': failed_jobs,
                        'review': review_jobs,
                    },
                    'recent_jobs': recent_jobs,
                    'timestamp': timezone.now().isoformat()
                }
            }

            print(f"📡 Sending admin update via WebSocket:")
            print(f"   Group: admin_updates")
            print(f"   Type: admin_update")
            print(f"   Data overview: {update_data['data']['overview']}")

            async_to_sync(channel_layer.group_send)(
                "admin_updates",
                {
                    "type": "admin_update",  # Fixed: matches consumer method name
                    "data": update_data
                }
            )
            print(f"✅ Admin update sent to WebSocket group")
        except Exception as e:
            logger.error(f"Failed to send admin live update: {e}")


@receiver(post_save, sender=QueuedJob)
def job_updated(sender, instance, created, **kwargs):
    """Send live update when a job is created or updated"""
    send_admin_live_update()


@receiver(post_delete, sender=QueuedJob)
def job_deleted(sender, instance, **kwargs):
    """Send live update when a job is deleted"""
    send_admin_live_update()