{% extends "admin/index.html" %}
{% load i18n %}

{% block content %}
<!-- Clean Admin Interface - Old dashboard removed -->
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; margin-bottom: 20px; border-radius: 8px;">
    <h2 style="margin-top: 0; color: white;">🎛️ Queue System Dashboards</h2>
    <p style="margin-bottom: 15px; opacity: 0.9;">Professional queue monitoring and management tools</p>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
        <a href="{% url 'queue_system:queue_overview' %}" style="background: rgba(255,255,255,0.2); color: white; padding: 15px; text-decoration: none; border-radius: 6px; display: block; transition: all 0.3s;">
            <div style="font-size: 24px; margin-bottom: 5px;">📊</div>
            <div style="font-weight: bold; margin-bottom: 5px;">Queue Overview</div>
            <div style="font-size: 12px; opacity: 0.8;">Active queues, workers, success rates</div>
        </a>

        <a href="{% url 'queue_system:review_queue_dashboard' %}" style="background: rgba(255,255,255,0.2); color: white; padding: 15px; text-decoration: none; border-radius: 6px; display: block; transition: all 0.3s;">
            <div style="font-size: 24px; margin-bottom: 5px;">🔍</div>
            <div style="font-weight: bold; margin-bottom: 5px;">Review Queue</div>
            <div style="font-size: 12px; opacity: 0.8;">Failed jobs requiring admin review</div>
        </a>

        <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" style="background: rgba(255,255,255,0.2); color: white; padding: 15px; text-decoration: none; border-radius: 6px; display: block; transition: all 0.3s;">
            <div style="font-size: 24px; margin-bottom: 5px;">📋</div>
            <div style="font-weight: bold; margin-bottom: 5px;">All Jobs</div>
            <div style="font-size: 12px; opacity: 0.8;">Standard admin job management</div>
        </a>
    </div>
</div>

<style>
a[href*="queue"]:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: translateY(-2px);
}
</style>

{{ block.super }}
{% endblock %}