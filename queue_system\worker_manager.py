"""
Worker Manager - Auto-start Celery workers for location queues
This module handles automatic starting/stopping of workers when Django starts
"""

import subprocess
import threading
import time
import atexit
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

# Global variables to track workers
_workers = []
_beat_process = None
_workers_started = False
_worker_lock = threading.Lock()

def check_redis_connection():
    """Check if Redis is available"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        return True
    except Exception:
        return False

def start_location_workers():
    """Start Celery workers for each location automatically"""
    global _workers, _beat_process, _workers_started
    
    with _worker_lock:
        if _workers_started:
            logger.info("Workers already started, skipping...")
            return
        
        try:
            # Only start workers if Redis is available
            if not check_redis_connection():
                logger.info("🔄 Redis not available - skipping auto-start workers")
                return
            
            # Import here to avoid circular imports
            from locations.models import Location
            
            logger.info("🚀 Auto-starting Celery workers for location queues...")
            
            # Get all locations
            locations = Location.objects.all()
            
            for location in locations:
                queue_name = f'location.{location.id}'
                worker_name = f'auto_worker_{location.location_name}_{location.id}'
                
                # Build Celery worker command
                cmd = [
                    'celery', '-A', 'config', 'worker',
                    '--loglevel=info',
                    '--concurrency=1',
                    f'--queues={queue_name}',
                    f'--hostname={worker_name}@%h',
                    '--without-gossip',
                    '--without-mingle',
                    '--without-heartbeat'
                ]
                
                try:
                    # Start worker in background
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        universal_newlines=True
                    )
                    
                    _workers.append({
                        'name': worker_name,
                        'location': location.location_name,
                        'queue': queue_name,
                        'process': process,
                        'pid': process.pid
                    })
                    
                    logger.info(f"✅ Started worker {worker_name} for queue {queue_name} (PID: {process.pid})")
                    
                except Exception as e:
                    logger.warning(f"❌ Failed to start worker for {location.location_name}: {e}")
            
            # Start Celery Beat
            try:
                beat_cmd = [
                    'celery', '-A', 'config', 'beat',
                    '--loglevel=info',
                    '--scheduler=django_celery_beat.schedulers:DatabaseScheduler'
                ]
                
                _beat_process = subprocess.Popen(
                    beat_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                
                logger.info(f"📅 Started Celery Beat scheduler (PID: {_beat_process.pid})")
                
            except Exception as e:
                logger.warning(f"❌ Failed to start Celery Beat: {e}")
            
            _workers_started = True
            logger.info(f"🎉 Auto-started {len(_workers)} location workers + Beat scheduler")
            
        except Exception as e:
            logger.error(f"❌ Error auto-starting workers: {e}")

def stop_location_workers():
    """Stop all auto-started workers"""
    global _workers, _beat_process, _workers_started
    
    with _worker_lock:
        if not _workers_started:
            return
        
        logger.info("🛑 Stopping auto-started Celery workers...")
        
        # Stop workers
        for worker in _workers:
            try:
                worker['process'].terminate()
                logger.info(f"✅ Stopped worker {worker['name']} (PID: {worker['pid']})")
            except Exception as e:
                logger.warning(f"⚠️ Error stopping worker {worker['name']}: {e}")
        
        # Stop Beat
        if _beat_process:
            try:
                _beat_process.terminate()
                logger.info(f"✅ Stopped Celery Beat")
            except Exception as e:
                logger.warning(f"⚠️ Error stopping Celery Beat: {e}")
        
        _workers.clear()
        _beat_process = None
        _workers_started = False

def auto_start_workers_delayed():
    """Start workers after a short delay to ensure Django is fully loaded"""
    time.sleep(5)  # Wait for Django to fully initialize
    start_location_workers()

def get_worker_status():
    """Get status of auto-started workers"""
    global _workers, _beat_process
    
    status = {
        'workers_started': _workers_started,
        'total_workers': len(_workers),
        'running_workers': 0,
        'beat_running': False,
        'workers': []
    }
    
    for worker in _workers:
        is_running = worker['process'].poll() is None
        if is_running:
            status['running_workers'] += 1
        
        status['workers'].append({
            'name': worker['name'],
            'location': worker['location'],
            'queue': worker['queue'],
            'pid': worker['pid'],
            'running': is_running
        })
    
    if _beat_process:
        status['beat_running'] = _beat_process.poll() is None
    
    return status

# Register cleanup function
atexit.register(stop_location_workers)

# Auto-start workers in development mode
def initialize_auto_workers():
    """Initialize auto-workers if in development mode"""
    if settings.DEBUG:
        # Start workers in a separate thread after Django loads
        threading.Thread(target=auto_start_workers_delayed, daemon=True).start()
        logger.info("🔄 Scheduled auto-start of location workers in 5 seconds...")
