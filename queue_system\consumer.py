import json
import time
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import LocationQueueConfig, QueuedJob
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth.models import AnonymousUser

class QueueUpdatesConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        await self.channel_layer.group_add("queue_updates", self.channel_name)
        await self.send_initial_data()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard("queue_updates", self.channel_name)

    async def queue_update(self, event):
        await self.send(text_data=json.dumps(event['data']))

    @database_sync_to_async
    def get_queue_data(self):
        locations = LocationQueueConfig.objects.select_related('location').all()
        
        overview = {
            'total_active_jobs': QueuedJob.objects.filter(status='processing').count(),
            'total_waiting_jobs': QueuedJob.objects.filter(status='queued').count(),
            'total_active_workers': sum(loc.active_workers for loc in locations),
            'total_max_workers': sum(loc.max_workers for loc in locations),
            'locations': []
        }
        
        for loc in locations:
            jobs = QueuedJob.objects.filter(location=loc.location)
            completed = jobs.filter(status='completed').count()
            failed = jobs.filter(status='failed').count()
            total = completed + failed
            
            overview['locations'].append({
                'id': str(loc.location.id),
                'active_workers': loc.active_workers,
                'max_workers': loc.max_workers,
                'queued': jobs.filter(status='queued').count(),
                'processing': jobs.filter(status='processing').count(),
                'success_rate': round((completed / total) * 100, 1) if total > 0 else 100,
            })
        
        return overview

    async def send_initial_data(self):
        data = await self.get_queue_data()
        await self.send(text_data=json.dumps({
            'type': 'initial_data',
            'data': data
        }))


class AdminLiveUpdatesConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for live admin UI updates"""

    async def connect(self):
        # Check if user is authenticated and is staff
        user = self.scope["user"]
        print(f"🔗 WebSocket connection attempt from user: {user}")

        if isinstance(user, AnonymousUser) or not user.is_staff:
            print(f"❌ WebSocket rejected: user={user}, is_staff={getattr(user, 'is_staff', False)}")
            await self.close(code=4003)  # Custom close code for auth failure
            return

        print(f"✅ WebSocket accepted for admin user: {user.username}")
        await self.accept()
        await self.channel_layer.group_add("admin_updates", self.channel_name)

        try:
            await self.send_initial_admin_data()
            print(f"📊 Initial data sent to {user.username}")

            # Start periodic update check (workaround for InMemoryChannelLayer)
            self.last_update_time = time.time()
            self.update_task = asyncio.create_task(self.periodic_update_check())
        except Exception as e:
            print(f"❌ Error sending initial data: {e}")
            await self.close(code=4004)

    async def disconnect(self, close_code):
        user = self.scope.get("user", "Unknown")
        print(f"🔌 WebSocket disconnected: user={user}, code={close_code}")

        # Cancel periodic update task
        if hasattr(self, 'update_task'):
            self.update_task.cancel()

        await self.channel_layer.group_discard("admin_updates", self.channel_name)

    async def admin_update(self, event):
        """Handle admin update events"""
        try:
            print(f"📡 Consumer received admin_update event: {event}")
            print(f"📤 Sending data to WebSocket client: {json.dumps(event['data'])[:200]}...")
            await self.send(text_data=json.dumps(event['data']))
            print(f"✅ Successfully sent admin update to WebSocket client")
        except Exception as e:
            print(f"❌ Error sending admin update: {e}")
            import traceback
            traceback.print_exc()
            # Don't close connection for send errors, just log them

    @database_sync_to_async
    def get_admin_data(self):
        """Get comprehensive admin data for live updates"""
        # Get queue statistics
        total_jobs = QueuedJob.objects.count()
        queued_jobs = QueuedJob.objects.filter(status='queued').count()
        processing_jobs = QueuedJob.objects.filter(status='processing').count()
        completed_jobs = QueuedJob.objects.filter(status='completed').count()
        failed_jobs = QueuedJob.objects.filter(status='failed').count()
        review_jobs = QueuedJob.objects.filter(status='review').count()

        # Get recent jobs (last 10)
        recent_jobs = list(QueuedJob.objects.select_related('order', 'location')
                          .order_by('-created_at')[:10]
                          .values(
                              'id', 'status', 'retry_count', 'max_retries',
                              'created_at', 'failure_reason',
                              'order__first_name', 'order__surname',
                              'location__location_name'
                          ))

        # Convert datetime objects to strings for JSON serialization
        for job in recent_jobs:
            if job['created_at']:
                job['created_at'] = job['created_at'].isoformat()

        # Get location statistics
        locations = LocationQueueConfig.objects.select_related('location').all()
        location_stats = []

        for loc in locations:
            jobs = QueuedJob.objects.filter(location=loc.location)
            completed = jobs.filter(status='completed').count()
            failed = jobs.filter(status='failed').count()
            total = completed + failed

            location_stats.append({
                'id': str(loc.location.id),
                'name': loc.location.location_name,
                'active_workers': loc.active_workers,
                'max_workers': loc.max_workers,
                'queued': jobs.filter(status='queued').count(),
                'processing': jobs.filter(status='processing').count(),
                'completed': completed,
                'failed': failed,
                'review': jobs.filter(status='review').count(),
                'success_rate': round((completed / total) * 100, 1) if total > 0 else 100,
            })

        return {
            'overview': {
                'total_jobs': total_jobs,
                'queued': queued_jobs,
                'processing': processing_jobs,
                'completed': completed_jobs,
                'failed': failed_jobs,
                'review': review_jobs,
            },
            'recent_jobs': recent_jobs,
            'locations': location_stats,
            'timestamp': timezone.now().isoformat()
        }

    async def send_initial_admin_data(self):
        """Send initial data when admin connects"""
        data = await self.get_admin_data()
        await self.send(text_data=json.dumps({
            'type': 'initial_data',
            'data': data
        }))

    async def periodic_update_check(self):
        """Periodic check for updates (workaround for InMemoryChannelLayer)"""
        print(f"🔄 Starting periodic update check task")

        # Initialize last stats
        try:
            initial_data = await self.get_admin_data()
            self.last_stats = initial_data['overview']
            print(f"📊 Initial stats stored: {self.last_stats}")
        except Exception as e:
            print(f"❌ Error getting initial stats: {e}")
            self.last_stats = {}

        while True:
            try:
                await asyncio.sleep(2)  # Check every 2 seconds for faster updates
                print(f"🔍 Checking for data changes...")

                # Get current data
                current_data = await self.get_admin_data()
                current_stats = current_data['overview']

                print(f"📊 Current stats: {current_stats}")
                print(f"📊 Last stats: {self.last_stats}")

                # Check if any key metrics changed
                changed = False
                changes = []
                for key in ['total_jobs', 'queued', 'processing', 'completed', 'failed', 'review']:
                    current_val = current_stats.get(key, 0)
                    last_val = self.last_stats.get(key, 0)
                    if current_val != last_val:
                        changed = True
                        changes.append(f"{key}: {last_val} → {current_val}")

                if changed:
                    print(f"📊 Detected changes: {', '.join(changes)}")
                    print(f"📤 Sending update to WebSocket client...")

                    await self.send(text_data=json.dumps({
                        'type': 'admin_update',
                        'data': current_data
                    }))

                    self.last_stats = current_stats
                    print(f"✅ Update sent via periodic check")
                else:
                    print(f"⚪ No changes detected")

            except asyncio.CancelledError:
                print(f"🔄 Periodic update check cancelled")
                break
            except Exception as e:
                print(f"❌ Error in periodic update check: {e}")
                import traceback
                traceback.print_exc()
                await asyncio.sleep(5)  # Wait longer on error