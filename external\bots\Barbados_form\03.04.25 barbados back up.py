from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from datetime import datetime
from django.db.utils import OperationalError

import os
import time
import logging
import re
import django
import traceback
import sys

# Add the project to the system path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django Environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

# set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# Helper class for standardized element interactions.
class ElementHandler:
    
    # Initialize the element handler with the driver, wait, and logger.
    def __init__(self, driver, wait, logger):
        self.driver = driver
        self.wait = wait
        self.logger = logger

    # Fill text field with values from the database.
    def fill_text_field(self, xpath, value):
        try:
            element = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            element.clear()
            time.sleep(0.3)
            element.send_keys(value)
            time.sleep(0.3)
            self.logger.info(f"filled text field: {xpath} with value: {value}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error filling text field: {e}")
            return False
    
    # click element by XPATH.
    def click_element(self, xpath):
        try:
            element = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            element.click()
            self.logger.info(f"clicked element by: {xpath}")
            return True
        except Exception as e:
            self.logger.error(f"Error clicking element: {e}")
            return False
        
    # click drop down element, and select a value from dropdown
    def select_dropdown_option(self, dropdown_xpath, option_text=None, option_value=None):
        try:
            # Click the dropdown to open it
            dropdown_element = self.wait.until(EC.element_to_be_clickable((By.XPATH, dropdown_xpath)))
            dropdown_element.click()
            self.logger.info(f"clicked dropdown element by: {dropdown_xpath}")
            time.sleep(0.3)

            # For this specific site, using keyboard navigation is more reliable
            # than trying to locate and click options
            if option_text:
                # Type the text
                dropdown_element.send_keys(option_text)
                self.logger.info(f"Typed text: {option_text}")
                time.sleep(0.3)
                
                # Press Down and Enter to select the first matching option
                dropdown_element.send_keys(Keys.DOWN)
                dropdown_element.send_keys(Keys.RETURN)
                self.logger.info(f"Selected option using keyboard: {option_text}")
                return True
                
            # Try clicking the option if text or value provided
            elif option_value:
                # Similar keyboard approach for value
                dropdown_element.send_keys(option_value)
                time.sleep(0.3)
                dropdown_element.send_keys(Keys.DOWN, Keys.RETURN)
                return True
                
            # If no text/value provided, just return the dropdown element for further handling
            else:
                return dropdown_element
                
        except Exception as e:
            self.logger.error(f"Error selecting dropdown option: {e}")
            return False

    # Calander elements: select date
    def select_date(self, xpath, date_value):
        """Select a date from a date picker with fallback to direct input"""
        try:
            # Try to select from calendar first
            calendar_success = self._try_calendar_selection(xpath, date_value)
            
            if calendar_success:
                return True
                
            # If calendar selection fails, try direct input as fallback
            self.logger.info(f"Calendar selection failed for {date_value}. Trying direct input...")
            return self.fill_date_field_directly(xpath, date_value)
                
        except Exception as e:
            self.logger.error(f"Error in date selection: {e}")
            # Try direct input as last resort
            try:
                return self.fill_date_field_directly(xpath, date_value)
            except Exception as e2:
                self.logger.error(f"Both date selection methods failed: {e2}")
                return False

    # Try to select date using calander widget
    def _try_calendar_selection(self, xpath, date_value):
        """Try to select a date using the calendar widget"""
        try:
            # Parse the date to datetime object
            if isinstance(date_value, str):
                try:
                    date_obj = datetime.strptime(date_value, '%Y-%m-%d')
                except ValueError:
                    try:
                        date_obj = datetime.strptime(date_value, '%d/%m/%Y')
                    except ValueError:
                        date_obj = datetime.strptime(date_value, '%m/%d/%Y')
            elif hasattr(date_value, 'year'):
                date_obj = date_value
            else:
                self.logger.error(f"Unsupported date format: {date_value}")
                return False
                
            # Format the date components
            target_month_year = date_obj.strftime('%B %Y')
            target_day = str(date_obj.day)
            self.logger.info(f"Formatted date: Day={target_day}, Month/Year={target_month_year}")
            
            # Click to open the calendar
            date_input_element = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            date_input_element.click()
            self.logger.info("Clicked on calendar element to open it")
            time.sleep(1.0)
            
            # Find the month/year header
            header_xpath = "//button[contains(@class, 'mantine-DatePickerInput-calendarHeaderLevel')]"
            month_header = self.wait.until(EC.visibility_of_element_located((By.XPATH, header_xpath)))
            
            # Navigate to find the correct month and year
            current_month_year = month_header.text
            self.logger.info(f"Current month and year: {current_month_year}")
            
            # Click through months until we find our target
            attempts = 0
            while current_month_year != target_month_year and attempts < 24:  # Limit to 24 attempts (2 years)
                next_btn = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[@data-direction='next']"))
                )
                next_btn.click()
                self.logger.info("Clicked next month button")
                time.sleep(0.5)
                
                # Update current month/year text
                current_month_year = self.wait.until(EC.visibility_of_element_located(
                    (By.XPATH, header_xpath))).text
                self.logger.info(f"Updated month and year: {current_month_year}")
                attempts += 1
            
            if attempts >= 24:
                self.logger.error("Could not find target month/year after 24 attempts")
                return False
                
            self.logger.info("Found the correct month and year")
            
            # Now select the day
            day_xpaths = [
                f"//button[@aria-label='{target_day} {target_month_year}']",
                f"//div[contains(@class, 'mantine-DatePickerInput-monthsList')]//button[contains(@class, 'mantine-DatePickerInput-day') and not(contains(@class, 'outside')) and normalize-space(.)='{target_day}']",
                f"//button[contains(@class, 'mantine-DatePickerInput-day') and not(contains(@class, 'outside')) and text()='{target_day}']"
            ]
            
            for day_xpath in day_xpaths:
                try:
                    self.logger.info(f"Trying to find day with xpath: {day_xpath}")
                    day_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, day_xpath)))
                    day_button.click()
                    self.logger.info(f"Selected day: {target_day}")
                    return True
                except Exception:
                    continue
                    
            self.logger.error(f"Day {target_day} button not found")
            return False
            
        except Exception as e:
            self.logger.error(f"Error in calendar selection: {e}")
            return False    

    # Calander elements: select month
    def _navigate_to_month(self, target_month):
        try:
            time.sleep(0.5)
            # Try to find the month selector
            month_short = target_month[:3]  # First three letters
            
            try:
                month_buttons = self.wait.until(
                    EC.presence_of_all_elements_located(
                        (By.XPATH, "//td[contains(@class, 'mantine-DatePickerInput-monthsListCell')]//button")
                    )
                )
                
                for btn in month_buttons:
                    if btn.text.strip() == month_short:
                        btn.click()
                        self.logger.info(f"Selected month: {target_month}")
                        return True
                        
                self.logger.error(f"Month {target_month} not found")
                return False
                
            except Exception as e:
                self.logger.error(f"Error finding month buttons: {e}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in _navigate_to_month: {e}")
            return False

    # Calander elements: select day    
    def _select_day(self, day, month, year):
        try:
            time.sleep(0.5)
            
            # Try different XPath patterns to find the day
            day_xpaths = [
                f"//button[@aria-label='{day} {month} {year}']",
                f"//div[contains(@class, 'mantine-DatePickerInput-monthsList')]//button[contains(@class, 'mantine-DatePickerInput-day') and not(contains(@class, 'outside')) and normalize-space(.)='{day}']"
            ]
            
            for xpath in day_xpaths:
                try:
                    day_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
                    day_button.click()
                    self.logger.info(f"Selected day: {day}")
                    return True
                except Exception:
                    continue
                    
            self.logger.error(f"Day {day} button not found")
            return False
            
        except Exception as e:
            self.logger.error(f"Error in _select_day: {e}")
            return False

    # Calander elements: navigate to year
    def _navigate_to_year(self, target_year):
        try:
            # Simpler approach - just click header and search for year directly
            try:
                # Click on calendar header to access year view
                header = self.driver.find_element(By.XPATH, "//button[contains(@class, 'calendarHeaderLevel')]")
                header.click()
                self.logger.info("Clicked year header")
                time.sleep(0.5)
                
                # Find and click the year directly using a more generic selector
                year_button = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{target_year}')]")
                year_button.click()
                self.logger.info(f"Selected year: {target_year}")
                return True
            except Exception as e:
                # If direct selection fails, log it but continue to month selection
                self.logger.info(f"Could not select year directly: {e}")
                return True  # Return true to continue with month selection
        except Exception as e:
            self.logger.error(f"Error in year navigation: {e}")
            return True  # Continue anyway to prevent calendar from failing completely

        # Calander elements: Expiry date
    
    # Calander elements: expiry date
    def travel_document_expiry_element(self, xpath, expiry_date):
        try:
            self.logger.info(f"Handling expiry date: {expiry_date}")
            
            # Parse the date
            if isinstance(expiry_date, str):
                try:
                    date_obj = datetime.strptime(expiry_date, '%Y-%m-%d')
                except ValueError:
                    try:
                        date_obj = datetime.strptime(expiry_date, '%d/%m/%Y')
                    except ValueError:
                        date_obj = datetime.strptime(expiry_date, '%m/%d/%Y')
            elif hasattr(expiry_date, 'year'):
                date_obj = expiry_date
            else:
                self.logger.error(f"Unsupported date format: {expiry_date}")
                return False
            
            # Extract components
            target_year = str(date_obj.year)
            target_month = date_obj.strftime('%b')  # Abbreviated month name
            target_day = str(date_obj.day)
            
            # Open the date picker
            date_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            date_input.click()
            self.logger.info("Clicked expiry date field to open picker")
            time.sleep(1)
            
            # Check if we're in decade view or month view
            try:
                decade_header = self.wait.until(EC.visibility_of_element_located(
                    (By.XPATH, "//div[contains(@class, 'mantine-DatePickerInput-calendarHeaderLevel')]")
                ))
                
                current_view_text = decade_header.text.strip()
                self.logger.info(f"Current calendar view: {current_view_text}")
                
                # If we're in decade view (shows years like "2020 - 2029")
                if " – " in current_view_text or " - " in current_view_text:
                    # We're in decade view
                    target_decade_start = (date_obj.year // 10) * 10
                    
                    # Navigate to the correct decade if needed
                    while not current_view_text.startswith(str(target_decade_start)):
                        # Since we're going to future dates, we might need "next" button
                        next_button = self.wait.until(EC.element_to_be_clickable(
                            (By.XPATH, "//button[@data-direction='next']")
                        ))
                        next_button.click()
                        time.sleep(0.5)
                        
                        decade_header = self.wait.until(EC.visibility_of_element_located(
                            (By.XPATH, "//div[contains(@class, 'mantine-DatePickerInput-calendarHeaderLevel')]")
                        ))
                        current_view_text = decade_header.text.strip()
                        self.logger.info(f"Updated decade view: {current_view_text}")
                    
                    # Select the year
                    year_button_xpath = f"//button[contains(@class, 'yearsListControl') and text()='{target_year}']"
                    year_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, year_button_xpath)))
                    year_button.click()
                    self.logger.info(f"Selected year: {target_year}")
                    time.sleep(1)
                
                # Now we should be in month view
                # Select the month
                month_button_xpath = f"//button[contains(@class, 'monthsListControl') and text()='{target_month}']"
                month_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, month_button_xpath)))
                month_button.click()
                self.logger.info(f"Selected month: {target_month}")
                time.sleep(1)
                
                # Select the day
                day_button_xpath = f"//button[contains(@class, 'mantine-DatePickerInput-day') and not(contains(@class, 'outside')) and text()='{target_day}']"
                day_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, day_button_xpath)))
                day_button.click()
                self.logger.info(f"Selected day: {target_day}")
                
                return True
                
            except Exception as e:
                self.logger.error(f"Error in calendar navigation: {e}")
                
                # Fall back to JavaScript method
                self.logger.info("Falling back to JavaScript method for expiry date")
                return self.fill_date_field_directly(xpath, expiry_date)
                
        except Exception as e:
            self.logger.error(f"Error handling expiry date: {e}")
            return False    

    # Fallback to fill out date directly
    def fill_date_field_directly(self, xpath, date_value):
        try:
            # Parse the date
            if isinstance(date_value, str):
                try:
                    date_obj = datetime.strptime(date_value, '%Y-%m-%d')
                except ValueError:
                    try:
                        date_obj = datetime.strptime(date_value, '%d/%m/%Y')
                    except ValueError:
                        date_obj = datetime.strptime(date_value, '%m/%d/%Y')
            elif hasattr(date_value, 'year'):
                date_obj = date_value
            else:
                self.logger.error(f"Unsupported date format: {date_value}")
                return False

            # Format for direct input
            formatted_date = date_obj.strftime('%m/%d/%Y')

            # Find the element
            date_field = self.wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
            
            # Use JavaScript to set the value directly (this bypasses read-only restrictions)
            self.driver.execute_script(f"arguments[0].value = '{formatted_date}';", date_field)
            self.logger.info(f"Set date value with JavaScript: {formatted_date}")
            
            # Trigger change event to ensure the form recognizes the change
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", date_field)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('blur', { bubbles: true }));", date_field)
            
            # Tab out of the field
            date_field.send_keys(Keys.TAB)
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error directly filling date field: {e}")
            return False

    # 'Next Button' move to next stage
    def click_next_button(self, xpath):
        try:
            click_next_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            click_next_button.click()
            self.logger.info(f"Found, and clicked element by XPATH: {xpath}")
            return True
        except Exception as e:
            self.logger.error(f"Error clicking element: {xpath}")
            return False


    def select_multiple_options(self, xpath, options_list):
        try:
            # Click the multi-select field to expand the dropdown
            multi_select_field = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            multi_select_field.click()
            self.logger.info("Clicked multi-select dropdown")
            time.sleep(0.5)
            
            # Handle "None of the listed countries" special case
            if options_list.strip().upper() == "NONE OF THE LISTED COUNTRIES":
                try:
                    none_option = self.wait.until(
                        EC.element_to_be_clickable(
                            (By.XPATH, "//div[@data-combobox-option='true' and .//span[text()='None of the listed countries']]")
                        )
                    )
                    none_option.click()
                    self.logger.info("Selected 'None of the listed countries'")
                    return True
                except Exception as e:
                    self.logger.error(f"'None of the listed countries' option not found: {e}")
                    return False
            else:
                # Convert string to list
                countries_list = [country.strip() for country in options_list.split(",")]
                
                for country in countries_list:
                    try:
                        # Re-find the multi-select field before each country selection
                        multi_select_field = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, xpath))
                        )
                        multi_select_field.click()
                        time.sleep(0.5)
                        
                        # Build XPath based on the country name (comparing in upper-case)
                        xpath_option = (
                            f"//div[@data-combobox-option='true' and .//span[contains(translate(text(), "
                            f"'abcdefghijklmnopqrstuvwxyz', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'), '{country.upper()}')]]"
                        )
                        country_option = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, xpath_option))
                        )
                        country_option.click()
                        self.logger.info(f"Selected country: {country}")
                        time.sleep(0.3)
                    except Exception as e:
                        self.logger.error(f"Option for country '{country}' not found: {e}")
                        return False
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error selecting multiple options: {e}")
            return False

    # Verifying form selections and answers
    def verify_input_field(self, xpath, expected_value):
        try:
            element = self.wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
            
            # Try different attributes to get the value
            actual_value = element.get_attribute("value")
            if not actual_value:
                # For some dropdown elements, the displayed text might be in a different attribute
                actual_value = element.text
                
            # Sometimes the value is in a child element
            if not actual_value:
                try:
                    actual_value = element.find_element(By.XPATH, ".//input").get_attribute("value")
                except:
                    pass
                    
            self.logger.info(f"Verifying field {xpath}: Expected '{expected_value}', Found '{actual_value}'")
            
            # Sometimes the exact match isn't possible, so check if the expected value is contained
            if actual_value and expected_value.lower() in actual_value.lower():
                self.logger.info(f"Field verification successful: {xpath}")
                return True
            else:
                self.logger.warning(f"Field verification failed: {xpath}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error verifying field {xpath}: {e}")
            return False        


# main class for the Barbados farm filling bot. 
class BarbadosFormBot:

    # ---- Locators ----
    # Stage 1: Arrival

    # Residential Status IDE
    RESIDENTIAL_STATUS_PERMANENT_XPATH = "//label[.//span[text()='Permanent Address Is In Barbados']]"
    RESIDENTIAL_STATUS_VISITOR_XPATH = "//label[.//span[text()='Non-Resident/Visitor']]"
    # Entry Method IDE
    ENTRY_METHOD_AIR_XPATH = "//label[.//span[text()='Air']]"
    ENTRY_METHOD_SEA_XPATH = "//label[.//span[text()='Sea']]"
    # Airline
    AIRLINE_DROPDOWN_XPATH = "//*[@data-path='arrival.carrierName']"
    # Flight number
    FLIGHT_NUMBER_XPATH = "//*[@data-path='arrival.carrierID']"
    # Vessel details
    VESSEL_NAME_XPATH = "//*[@data-path='arrival.carrierName']"
    VESSEL_NUMBER_XPATH = "//*[@data-path='arrival.carrierID']"
    # Country of embarkation
    COUNTRY_OF_EMBARKATION_XPATH = "//*[@data-path='arrival.embarkCountry']"
    # Port of embarkation
    PORT_OF_EMBARKATOIN_XPATH = "//*[@data-path='arrival.embarkPort']"
    # Intended date of arrival
    INTENDED_ARRIVAL_DATE_XPATH = "//*[@data-path='arrival.travelDate']"
    # First name text field
    FIRST_NAME_XPATH = "//*[@data-path='personal.firstName']"
    # Surname text field
    SURNAME_XPATH = "//*[@data-path='personal.lastName']"
    # Gender dropdown element
    GENDER_DROPDOWN_XPATH = "//*[@data-path='personal.gender']"
    # Country of birth element
    COUNTRY_OF_BIRTH_XPATH = "//*[@data-path='personal.countryOfBirth']"
    # Date of birth element
    DATE_OF_BIRTH_XPATH = "//*[@data-path='personal.dob']"
    # Nationality element
    NATIONALITY_XPATH = "//*[@data-path='personal.nationality']"
    # Country of residence
    COUNTRY_OF_RESIDENCE_XPATH = "//*[@data-path='personal.countryOfResidence']"
    # Zip code
    ZIP_CODE_XPATH = "//*[@data-path='personal.zipCode']"
    # Email address
    EMAIL_ADDRESS_XPATH = "//*[@data-path='personal.email']"
    # Confirm email address
    CONFIRM_EMAIL_ADDRESS_XPATH = "//*[@data-path='personal.confirmEmail']"
    # Phone number field
    PHONE_NUMBER_XPATH = "//input[@type='tel']"
    # Authorize button
    AUTHORIZE_CHECKBOX_XPATH = "//*[@data-path='personal.authorizeUse']"
    # Travel document type dropdow
    TRAVEL_DOCUMENT_TYPE_XPATH = "//*[@data-path='travel.documentType']"
    # Travel document number field
    TRAVEL_DOCUMENT_NUMBER_XPATH = "//*[@data-path='travel.documentNum']"
    # Expiry date element
    TRAVEL_DOCUMENT_EXPIRY_XPATH = "//*[@data-path='travel.expiryDt']"
    # Visited countries element
    VISITED_COUNTRIES_XPATH = "//div[contains(@class, 'mantine-MultiSelect-wrapper')]"
    #Purpose of visit dropdown
    PURPOSE_OF_VISIT_XPATH = "//*[@data-path='destination.purposeOfVisit']"
    # Accommodation Type dropdown
    ACCOMMODATION_TYPE_XPATH = "//*[@data-path='destination.accommodationType']"
    # Accommodation Name dropdown
    ACCOMMODATION_NAME_XPATH = "//*[@data-path='destination.accommodationName']"
    # Accommodation Address text field    
    ACCOMMODATION_ADDRESS_XPATH = "//*[@data-path='destination.destinationAddressline1']"
    # Specify Accommodation type text field
    SPECIFY_ACCOMMODATION_TYPE_XPATH = "//*[@data-path='destination.specifyAccommodation']"

    # Next stage button
    NEXT_STAGE_BUTTON = "//button[@type='submit' and contains(@class, 'bg-blue-500')]"


        # Initialize the bot with form data.
    def __init__(self, form_data):
        self.form_data = form_data
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)
        self.element_handler = None

    # --- Internal Helper Methods for DRY ---

    def _get_data(self, attribute_path, log_label):
        """Safely retrieves data from form_data, handling nested attributes."""
        try:
            attributes = attribute_path.split('.')
            value = self.form_data
            for attr in attributes:
                value = getattr(value, attr)
            self.logger.info(f"Extracted {log_label}: {value}")
            return value
        except AttributeError:
            self.logger.error(f"Attribute path '{attribute_path}' not found in form_data.")
            return None
        except Exception as e:
            self.logger.error(f"Error getting data for {log_label} ({attribute_path}): {e}")
            return None

    def _fill_text_field_wrapper(self, field_label, data_attribute_path, element_xpath):
        """DRY wrapper for getting data and filling a text field."""
        try:
            value = self._get_data(data_attribute_path, field_label)
            if value is None: # Check if _get_data failed
                 return False
            if self.element_handler.fill_text_field(element_xpath, value):
                self.logger.info(f"Successfully entered {field_label}: {value}")
                return True
            else:
                self.logger.error(f"Failed to enter {field_label}: {value}")
                return False
        except Exception as e:
            self.logger.error(f"Error handling {field_label} text field: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _select_dropdown_wrapper(self, field_label, data_attribute_path, element_xpath):
        """DRY wrapper for getting data and selecting a dropdown option by text."""
        try:
            value = self._get_data(data_attribute_path, field_label)
            if value is None:
                return False
            # Assuming select_dropdown_option uses option_text by default when value is passed
            if self.element_handler.select_dropdown_option(element_xpath, option_text=value):
                self.logger.info(f"Successfully selected {field_label}: {value}")
                return True
            else:
                self.logger.error(f"Failed to select {field_label}: {value}")
                return False
        except Exception as e:
            self.logger.error(f"Error handling {field_label} dropdown: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _handle_radio_group(self, field_label, data_attribute_path, value_xpath_map):
        """DRY wrapper for handling radio button groups."""
        try:
            value = self._get_data(data_attribute_path, field_label)
            if value is None:
                return False

            if value in value_xpath_map:
                xpath_to_click = value_xpath_map[value]
                if self.element_handler.click_element(xpath_to_click):
                    self.logger.info(f"Successfully selected {field_label}: {value}")
                    return True
                else:
                    self.logger.error(f"Failed to click element for {field_label}: {value}")
                    return False
            else:
                self.logger.error(f"Invalid value for {field_label}: {value}")
                return False
        except Exception as e:
            self.logger.error(f"Error handling {field_label} radio group: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _call_complex_handler(self, handler_method, error_message_base):
        """DRY wrapper for calling complex handler methods."""
        try:
            if not handler_method():
                self.logger.error(f"{error_message_base} failed.")
                return False
            self.logger.info(f"{error_message_base} handled successfully.")
            return True
        except Exception as e:
            self.logger.error(f"Exception during {error_message_base}: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def click_element_js(self, xpath=None, element=None):
        """Clicks an element using JavaScript."""
        try:
            if element is None and xpath is not None:
                try:
                    element = self.wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
                except Exception as e:
                    self.logger.error(f"Error finding element for JS click: {e}")
                    return False
                    
            if element is not None:
                try:
                    self.driver.execute_script("arguments[0].click();", element)
                    self.logger.info(f"Clicked element with JavaScript: {xpath if xpath else 'element provided'}")
                    return True
                except Exception as e:
                    self.logger.error(f"Error executing JS click: {e}")
                    return False
            else:
                self.logger.error("Neither xpath nor element provided for JS click")
                return False
        except Exception as e:
            self.logger.error(f"Unexpected error in click_element_js: {e}")
            return False

    # --- End Internal Helper Methods ---


    # Initialize the web driver and navigate to the website.
    def initialize(self):
        try:
            # initiate the webdriver.
            self.driver = webdriver.Chrome()
            self.wait = WebDriverWait(self.driver, 20)

            # initialize the ElementHanlder
            self.element_handler = ElementHandler(self.driver, self.wait, self.logger)

            # navigate to the website.
            self.driver.get("https://www.travelform.gov.bb/create")
            self.logger.info("Navigated to the Barbados Immigration Form Website.")
            return True

        except Exception as e:
            self.logger.error("An error occurred while initializing the bot.")
            self.logger.error(traceback.format_exc())
            self.cleanup()
            return False

    # Clean up when done or when an error occurs.
    def cleanup(self):
        if self.driver:
            self.driver.quit()
            self.logger.info("Webdriver closed.")

    # --- Complex Field Handlers ---

    # In the BarbadosFormBot class, outside the run() method:
    def handle_date_of_birth(self):
        try:
            date_of_birth = self.form_data.date_of_birth # Using direct access here, could use _get_data if preferred
            self.logger.info(f"Extracted date of birth from the database: {date_of_birth}")

            # Parse the date
            if isinstance(date_of_birth, str):
                try:
                    date_obj = datetime.strptime(date_of_birth, '%Y-%m-%d')
                except ValueError:
                    try:
                        date_obj = datetime.strptime(date_of_birth, '%d/%m/%Y')
                    except ValueError:
                        date_obj = datetime.strptime(date_of_birth, '%m/%d/%Y')
            elif hasattr(date_of_birth, 'year'):
                date_obj = date_of_birth
            else:
                self.logger.error(f"Unsupported date format: {date_of_birth}")
                return False

            # Extract components
            target_year = str(date_obj.year)
            target_month = date_obj.strftime('%b')  # Abbreviated month name
            target_day = str(date_obj.day)

            # Open the date picker
            date_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, self.DATE_OF_BIRTH_XPATH)))
            date_input.click()
            self.logger.info("Clicked date field to open picker")
            time.sleep(1)

            # 1. Click the header twice to get to decade view
            header_level_xpath = "//div[contains(@class, 'mantine-DatePickerInput-calendarHeaderLevel')]"
            header_level = self.wait.until(EC.element_to_be_clickable((By.XPATH, header_level_xpath)))
            header_level.click()
            self.logger.info("Clicked header to go to year view")
            time.sleep(0.5)
            header_level = self.wait.until(EC.element_to_be_clickable((By.XPATH, header_level_xpath))) # Re-find
            header_level.click()
            self.logger.info("Clicked header again to go to decade view")
            time.sleep(0.5)


            # 2. Navigate decades
            decade_header = self.wait.until(EC.visibility_of_element_located((By.XPATH, header_level_xpath)))
            current_decade_text = decade_header.text.strip()
            self.logger.info(f"Initial decade view: {current_decade_text}")

            # Extract the start year of the target decade
            target_decade_start = (date_obj.year // 10) * 10

            # Keep clicking "previous" or "next" until we reach the target decade
            attempts = 0
            prev_button_xpath = "//button[@data-direction='previous']"
            next_button_xpath = "//button[@data-direction='next']"

            while attempts < 30:
                # Parse current decade range (e.g., "1990 – 1999")
                try:
                    current_decade_start = int(current_decade_text.split('–')[0].strip())
                    if current_decade_start == target_decade_start:
                        self.logger.info(f"Found correct decade range: {current_decade_text}")
                        break
                    elif current_decade_start > target_decade_start:
                        # Click previous decade button
                        nav_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, prev_button_xpath)))
                        nav_button.click()
                        self.logger.info(f"Clicked previous decade button, attempt {attempts+1}")
                    else: # current_decade_start < target_decade_start
                        # Click next decade button
                        nav_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, next_button_xpath)))
                        nav_button.click()
                        self.logger.info(f"Clicked next decade button, attempt {attempts+1}")

                    time.sleep(0.5)

                    # Update decade text
                    decade_header = self.wait.until(EC.visibility_of_element_located((By.XPATH, header_level_xpath)))
                    current_decade_text = decade_header.text.strip()
                    self.logger.info(f"Current decade range: {current_decade_text}")

                except (ValueError, IndexError) as parse_error:
                    self.logger.error(f"Could not parse decade text '{current_decade_text}': {parse_error}")
                    # Fallback? Maybe try clicking previous anyway?
                    nav_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, prev_button_xpath)))
                    nav_button.click()
                    time.sleep(0.5)
                    decade_header = self.wait.until(EC.visibility_of_element_located((By.XPATH, header_level_xpath)))
                    current_decade_text = decade_header.text.strip()


                attempts += 1

            if attempts >= 30:
                self.logger.error("Could not navigate to the correct decade")
                # Attempt fallback to direct fill? Or just fail?
                return self.element_handler.fill_date_field_directly(self.DATE_OF_BIRTH_XPATH, date_of_birth)


            # 3. Select the year
            year_button_xpath = f"//button[contains(@class, 'yearsListControl') and text()='{target_year}']"
            year_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, year_button_xpath)))
            year_button.click()
            self.logger.info(f"Selected year: {target_year}")
            time.sleep(0.5) # Reduced sleep

            # 4. Select the month
            month_button_xpath = f"//button[contains(@class, 'monthsListControl') and text()='{target_month}']"
            month_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, month_button_xpath)))
            month_button.click()
            self.logger.info(f"Selected month: {target_month}")
            time.sleep(0.5) # Reduced sleep

            # 5. Select the day
            day_button_xpath = f"//button[contains(@class, 'mantine-DatePickerInput-day') and not(contains(@class, 'outside')) and text()='{target_day}']"
            # Ensure the day is visible and clickable within the current month view
            day_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, day_button_xpath)))
            day_button.click()
            self.logger.info(f"Selected day: {target_day}")

            return True

        except Exception as e:
            self.logger.error(f"Error handling date of birth: {e}")
            self.logger.error(traceback.format_exc())
            # Fallback to direct fill
            self.logger.info("Falling back to JavaScript method for date of birth")
            return self.element_handler.fill_date_field_directly(self.DATE_OF_BIRTH_XPATH, date_of_birth)


    def handle_visited_countries(self):
        """Handles the multi-select for visited countries."""
        try:
            visited_countries_str = self._get_data("visited_countries", "Visited Countries")
            if visited_countries_str is None:
                # If data is missing, maybe it's okay to skip? Or should return False?
                # Assuming empty/None means "None of the listed countries" is NOT selected.
                self.logger.warning("No visited countries data found.")
                # Check if the field is mandatory. If so, return False.
                # If not mandatory, maybe return True? For now, assume optional.
                return True # Or False if mandatory

            # Use the generic multi-select handler from ElementHandler
            if not self.element_handler.select_multiple_options(self.VISITED_COUNTRIES_XPATH, visited_countries_str):
                self.logger.error(f"Failed to select visited countries: {visited_countries_str}")
                return False

            self.logger.info(f"Successfully handled visited countries: {visited_countries_str}")
            return True

        except Exception as e:
            self.logger.error(f"Error handling visited countries: {e}")
            self.logger.error(traceback.format_exc())
            return False


    def handle_dropdown_with_complex_options(self, field_name, data_path, dropdown_xpath):
        try:
            # Debug: Print the exact XPath being used
            self.logger.info(f"DEBUG - Dropdown XPath being used: '{dropdown_xpath}'")
            
            value = self._get_data(data_path, field_name)
            if value is None:
                return False

            value_original = value
            value_uppercase = value.upper()
            self.logger.info(f"Attempting to select {field_name}: {value_original}")

            # 1. Click the main dropdown to open it
            self.logger.info(f"Attempting to click dropdown input: {dropdown_xpath}")
            if not self.click_element_js(dropdown_xpath):
                if not self.element_handler.click_element(dropdown_xpath):
                    self.logger.error(f"Both JS and regular click failed on {field_name} dropdown.")
                    return False
            self.logger.info(f"Clicked {field_name} dropdown to open.")
            time.sleep(1)

            # --- Selection Strategies ---
            found = False
            option_element = None # Initialize variable

            # Strategy 1: Find specific option by exact text XPath
            try:
                option_xpath = f"//div[@role='option'][translate(normalize-space(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz')=translate('{value_original}', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz')]"
                self.logger.info(f"Trying to find option with case-insensitive XPath: {option_xpath}")
                # Use WebDriverWait directly here to find the element *before* trying to click
                # This is because we need the element object for potential JS click fallback
                option_element = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, option_xpath)),
                    message=f"Option '{value_uppercase}' (Exact XPath) not clickable"
                )

                if option_element:
                    self.logger.info("Found option via exact XPath. Attempting click...")
                    try:
                        # Try direct click first
                        option_element.click()
                        self.logger.info("Direct click successful (Exact XPath).")
                        found = True
                        time.sleep(0.5)
                    except Exception as direct_click_err:
                        self.logger.warning(f"Direct click failed (Exact XPath): {direct_click_err}. Trying JS click.")
                        # Pass the *found element* to click_element_js
                        if self.click_element_js(element=option_element):
                            self.logger.info("JS click successful (Exact XPath).")
                            found = True
                            time.sleep(0.5)
                        else:
                            self.logger.warning("JS click also failed (Exact XPath).")
            except TimeoutException:
                self.logger.warning(f"Could not find clickable option with exact XPath: {option_xpath}")
            except Exception as xpath_err:
                self.logger.warning(f"Exact XPath strategy failed with unexpected error: {xpath_err}")


            # Strategy 2: Iterate through all options (if exact XPath failed)
            if not found:
                self.logger.info("Trying iteration strategy...")
                try:
                    all_options_xpath = "//div[@role='option']"
                    # Wait for at least one option to be present
                    self.wait.until(EC.presence_of_element_located((By.XPATH, all_options_xpath)))
                    all_options = self.driver.find_elements(By.XPATH, all_options_xpath)
                    self.logger.info(f"Found {len(all_options)} options to iterate through.")

                    for option in all_options:
                        option_text = ""
                        try:
                            # Get text robustly
                            option_text = option.text.upper().strip()
                            # Check visibility (optional but can help)
                            # if not option.is_displayed(): continue
                        except Exception as get_text_err:
                            self.logger.warning(f"Could not get text/check display for an option element: {get_text_err}. Skipping.")
                            continue

                        if value_uppercase in option_text:
                            self.logger.info(f"Found matching option via iteration: '{option.text}'. Attempting click...")
                            try:
                                # Ensure element is clickable before clicking
                                clickable_option = self.wait.until(EC.element_to_be_clickable(option))
                                clickable_option.click()
                                self.logger.info("Direct click successful (iteration).")
                                found = True
                                time.sleep(0.5)
                                break # Exit loop once found
                            except Exception as iter_click_err:
                                self.logger.warning(f"Direct click failed (iteration): {iter_click_err}. Trying JS click.")
                                # Pass the specific option element to JS click
                                if self.click_element_js(element=option):
                                    self.logger.info("JS click successful (iteration).")
                                    found = True
                                    time.sleep(0.5)
                                    break # Exit loop once found
                                else:
                                    self.logger.warning("JS click also failed (iteration).")
                        if found: break # Exit outer loop if found in inner try/except
                except TimeoutException:
                    self.logger.error("Timed out waiting for any options to appear for iteration.")
                except Exception as iter_err:
                    self.logger.error(f"Error during iteration strategy: {iter_err}")


            # Strategy 3: Broad JS Search (Fallback if others failed)
            if not found:
                self.logger.warning("Trying broad JS search strategy...")
                js_script = """
                var options = document.querySelectorAll('div[role="option"]');
                var targetText = arguments[0]; // Already uppercase
                for (var i = 0; i < options.length; i++) {{
                    // Use includes for partial matching, trim for whitespace robustness
                    if (options[i].textContent.trim().toUpperCase().includes(targetText)) {{
                        try {{
                            options[i].click();
                            return true; // Indicate success
                        }} catch (e) {{
                            console.error('JS click failed for option:', options[i].textContent, e);
                            // Optionally try scrolling into view if needed: options[i].scrollIntoView(); options[i].click();
                            return false; // Indicate failure on click error
                        }}
                    }}
                }}
                return false; // Indicate failure (not found)
                """
                try:
                    result = self.driver.execute_script(js_script, value_uppercase)
                    if result:
                        self.logger.info("Successfully selected via broad JS search.")
                        found = True
                        time.sleep(0.5)
                    else:
                        self.logger.error("Broad JS search also failed to find/select option.")
                except Exception as js_err:
                    self.logger.error(f"Error during broad JS search attempt: {js_err}")

            # --- Final Check ---
            if not found:
                self.logger.error(f"All strategies failed to select {field_name}: {value_uppercase}")
                return False
            else:
                self.logger.info(f"Successfully selected {field_name}: {value_uppercase}")
                # Optional: Add a verification step here if possible
                return True

        except Exception as e:
            self.logger.error(f"Error in {field_name} handling: {e}")
            self.logger.error(traceback.format_exc())
            return False








    # Main method to run the form filling process.
    def run(self):
        try:    
            if not self.initialize():
                self.logger.error("Failed to initialize the bot.")    
                return False

            ################################
            ####### STAGE 1: ARRIVAL #######
            ################################
            self.logger.info("Starting Stage 1: Arrival")

            # HANDLE RESIDENTIAL STATUS (Using Radio Group Helper)
            residential_map = {
                "Permanent Address Is In Barbados": self.RESIDENTIAL_STATUS_PERMANENT_XPATH,
                "Non-Resident/Visitor": self.RESIDENTIAL_STATUS_VISITOR_XPATH
            }
            if not self._handle_radio_group("Residential Status", "residential_status", residential_map):
                return False # Stop if it fails
            entry_method_map = {
                "Air": self.ENTRY_METHOD_AIR_XPATH,
                "Sea": self.ENTRY_METHOD_SEA_XPATH
            }
            entry_method = self._get_data("how_are_you_entering", "Entry Method")
            if entry_method is None: 
                 return False
            if not self._handle_radio_group("Entry Method", "how_are_you_entering", entry_method_map):
                 return False # Stop if clicking fails

            # HANDLE AIRLINE/VESSEL DETAILS (Conditional Logic using helpers)
            if entry_method == "Air":
                if not self._select_dropdown_wrapper("Airline", "airline", self.AIRLINE_DROPDOWN_XPATH):
                    return False
                if not self._fill_text_field_wrapper("Flight Number", "flight_number", self.FLIGHT_NUMBER_XPATH):
                    return False
            elif entry_method == "Sea":
                if not self._fill_text_field_wrapper("Vessel Name", "vessel_name", self.VESSEL_NAME_XPATH):
                    return False
                if not self._fill_text_field_wrapper("Vessel Number", "vessel_number", self.VESSEL_NUMBER_XPATH):
                    return False

            # HANDLE COUNTRY OF EMBARKATION (Using Dropdown Helper)
            if not self._select_dropdown_wrapper("Country of Embarkation", "departure_country", self.COUNTRY_OF_EMBARKATION_XPATH):
                return False

            # HANDLE PORT OF EMBARKATION (Using Dropdown Helper)
            if not self._select_dropdown_wrapper("Port of Embarkation", "departure_airport", self.PORT_OF_EMBARKATOIN_XPATH):
                 return False

            # HANDLE INTENDED DATE OF ARRIVAL (Still uses ElementHandler directly as it's complex)
            try:
                INTENDED_ARRIVAL_DATE = self._get_data("intended_arrival_date", "Intended Date of Arrival")
                if INTENDED_ARRIVAL_DATE is None: return False 
                if not self.element_handler.select_date(self.INTENDED_ARRIVAL_DATE_XPATH, INTENDED_ARRIVAL_DATE):
                    self.logger.error(f"Unable to select date from calendar: {INTENDED_ARRIVAL_DATE}")
                    return False
                self.logger.info(f"Successfully selected date from calendar: {INTENDED_ARRIVAL_DATE}")
            except Exception as e:
                self.logger.error(f"Error handling intended date of arrival {e}")
                self.logger.error(traceback.format_exc()) # Log the full traceback
                return False

            # HANDLE NEXT STEP BUTTON (Uses ElementHandler directly)
            if not self.element_handler.click_next_button(self.NEXT_STAGE_BUTTON):
                return False

            ################################
            ####### STAGE 2: PERSONAL ######
            ################################
            self.logger.info("Starting Stage 2: Personal")

            # HANDLE FIRST NAME (Using Text Field Helper)
            if not self._fill_text_field_wrapper("First Name", "order.first_name", self.FIRST_NAME_XPATH):
                return False 

            # HANDLE SURNAME (Using Text Field Helper)
            if not self._fill_text_field_wrapper("Surname", "order.surname", self.SURNAME_XPATH):
                return False

            # HANDLE GENDER DROPDOWN (Using Dropdown Helper)
            if not self._select_dropdown_wrapper("Gender", "gender", self.GENDER_DROPDOWN_XPATH):
                return False

            # COUNTRY OF BIRTH DROPDOWN (Using Dropdown Helper)
            if not self._select_dropdown_wrapper("Country of Birth", "country_of_birth", self.COUNTRY_OF_BIRTH_XPATH):
                return False

            # HANDLE DATE OF BIRTH (Using Complex Handler Wrapper)
            if not self._call_complex_handler(self.handle_date_of_birth, "Date of Birth"):
                 return False

            # HANDLE NATIONALITY (Using Dropdown Helper)
            if not self._select_dropdown_wrapper("Nationality", "nationality", self.NATIONALITY_XPATH):
                return False

            # COUNTRY OF RESIDENCE (Using Dropdown Helper)
            if not self._select_dropdown_wrapper("Country of Residence", "country_of_residence", self.COUNTRY_OF_RESIDENCE_XPATH):
                return False

            # HANDLE ZIP CODE (Using Text Field Helper)
            if not self._fill_text_field_wrapper("Zip Code", "zip_code", self.ZIP_CODE_XPATH):
                return False

            # HANDLE EMAIL ADDRESS (Using Text Field Helper)
            if not self._fill_text_field_wrapper("Email Address", "order.customer_email", self.EMAIL_ADDRESS_XPATH):
                return False

            # HANDLE CONFIRM EMAIL ADDRESS (Using Text Field Helper)
            if not self._fill_text_field_wrapper("Confirm Email Address", "order.customer_email", self.CONFIRM_EMAIL_ADDRESS_XPATH):
                return False

            # HANDLE PHONE NUMBER (Using Text Field Helper)
            if not self._fill_text_field_wrapper("Phone Number", "phone_number", self.PHONE_NUMBER_XPATH):
                return False

            # HANDLE AUTHORIZATION CHECKBOX (Simple click, keep direct call for now)
            if not self.element_handler.click_element(self.AUTHORIZE_CHECKBOX_XPATH):
                self.logger.error("Failed to click Authorization checkbox.")
                return False

            # HANDLE NEXT STEP BUTTON (Simple click, keep direct call for now)
            if not self.element_handler.click_element(self.NEXT_STAGE_BUTTON):
                self.logger.error("Failed to click Next button after Stage 2.") 
                return False
            
            ################################
            ######## STAGE 3: TRAVEL #######
            ################################
            self.logger.info("Starting Stage 3: Travel")

            # HANDLE TRAVEL DOCUMENT TYPE DROPDOWN (Value is hardcoded, so no data path needed)
            try:
                travel_document_type = "Passport" # Keep hardcoded value
                self.logger.info(f"Selecting travel document type: {travel_document_type}")
                # Use ElementHandler directly as we don't need to fetch data via _get_data
                if not self.element_handler.select_dropdown_option(self.TRAVEL_DOCUMENT_TYPE_XPATH, option_text=travel_document_type):
                    # Log error if select_dropdown_option returns False
                    self.logger.error(f"Unable to select travel document type: {travel_document_type}")
                    return False
                # Log success if it didn't return False
                self.logger.info(f"Successfully selected travel document type: {travel_document_type}")
            except Exception as e:
                # Catch any unexpected exceptions
                self.logger.error(f"Error handling travel document type dropdown: {e}")
                self.logger.error(traceback.format_exc())
                return False

            # HANDLE TRAVEL DOCUMENT NUMBER (Using Text Field Helper)
            if not self._fill_text_field_wrapper("Travel Document Number", "travel_document_number", self.TRAVEL_DOCUMENT_NUMBER_XPATH):
                return False

            # HANDLE EXPIRY DATE CALENDAR (Complex - Keep specific call, but use _get_data)
            try:
                # Use _get_data helper to retrieve and log the date value
                travel_document_expiry = self._get_data("travel_document_expiry", "Travel Document Expiry")
                if travel_document_expiry is None: return False # Stop if data retrieval failed

                # Call the specific expiry date selection method from ElementHandler
                if not self.element_handler.travel_document_expiry_element(self.TRAVEL_DOCUMENT_EXPIRY_XPATH, travel_document_expiry):
                    # Log error if travel_document_expiry_element returns False
                    self.logger.error(f"Failed to select expiry date: {travel_document_expiry}")
                    return False
                # Log success if it didn't return False
                self.logger.info(f"Successfully selected expiry date: {travel_document_expiry}")
            except Exception as e:
                # Catch any unexpected exceptions during the date handling process
                self.logger.error(f"Error handling expiry date calendar element: {e}")
                self.logger.error(traceback.format_exc())
                return False

            # HANDLE VISITED COUNTRIES (Using Complex Handler Wrapper)
            if not self._call_complex_handler(self.handle_visited_countries, "Visited Countries"):
                return False

            # HANDLE NEXT STEP BUTTON (Simple click, check return value)
            if not self.element_handler.click_element(self.NEXT_STAGE_BUTTON):
                self.logger.error("Failed to click Next button after Stage 3.") # Add specific log
                return False
                
            ################################
            ##### STAGE 4: DESTINATION #####
            ################################
            self.logger.info("Starting Stage 4: Destination")

            # HANDLE PURPOSE OF VISIT (Using Complex Handler Wrapper)
            if not self.handle_dropdown_with_complex_options("Purpose of Visit", "purpose_of_visit", self.PURPOSE_OF_VISIT_XPATH):
                self.logger.error("Failed during Purpose of Visit selection.")
                return False

            # HANDLE ACCOMMODATION TYPE (Using specific handler)
            if not self.handle_dropdown_with_complex_options("Accommodation Type", "accommodation_type", self.ACCOMMODATION_TYPE_XPATH):
                self.logger.error("Failed during Accommodation Type selection.")
                return False

            # --- Conditional Logic based on Accommodation Type ---
            accommodation_type = self._get_data("accommodation_type", "Accommodation Type (for conditional logic)")
            if accommodation_type is None:
                self.logger.error("Could not retrieve accommodation type after selection handler ran.")
                return False # Stop if we can't determine the type for the next steps

            accommodation_type_upper = accommodation_type.upper()

            time.sleep(2)

            # Handle Accommodation Name (if applicable)
            if accommodation_type_upper in ["APARTMENT", "GUEST HOUSE", "HOTEL"]:
                # Add explicit wait for the element to appear in DOM after selecting accommodation type
                try:
                    time.sleep(5)  # Increase initial wait time
                    self.logger.info("Waiting for accommodation name dropdown to be available...")
                    
                    # Use explicit wait with presence_of_element_located first
                    self.wait.until(EC.presence_of_element_located((By.XPATH, self.ACCOMMODATION_NAME_XPATH)))
                    time.sleep(1)  # Additional small wait after element is found
                    
                    # Then try to handle the dropdown
                    if not self.handle_dropdown_with_complex_options("Accommodation Name", "accommodation_name", self.ACCOMMODATION_NAME_XPATH):
                        self.logger.error("Failed during Accommodation Name selection.")
                        return False
                except TimeoutException:
                    self.logger.error("Accommodation name dropdown never appeared after selecting accommodation type")
                    return False

            # Handle Accommodation Address (if applicable)
            elif accommodation_type_upper in ["BED AND BREAKFAST", "PRIVATE HOME", "VILLA", "OTHER"]:
                 if not self._fill_text_field_wrapper("Accommodation Address", "accommodation_address", self.ACCOMMODATION_ADDRESS_XPATH):
                     return False

            # Handle Specify Accommodation (Only for "OTHER")
            if accommodation_type_upper == "OTHER":
                 if not self._fill_text_field_wrapper("Specify Accommodation Type", "specify_accommodation", self.SPECIFY_ACCOMMODATION_TYPE_XPATH):
                     self.logger.warning(f"Check if SPECIFY_ACCOMMODATION_TYPE_XPATH ({self.SPECIFY_ACCOMMODATION_TYPE_XPATH}) is a text field or dropdown.")
                     return False
                     
            time.sleep(10)


            
            self.logger.info("Form submission completed successfully.")
            return True

        except Exception as e:
            self.logger.error(f"Unexpected error during form submission: {str(e)}")
            self.logger.error(traceback.format_exc())                
            return False
            
        finally:
            self.cleanup()



# Entry point function for testing
def test_with_real_data():
    import traceback
    from orders.models import BarbadosForm, order

    try:
        # Find the Barbados form with the criminal check status of passed
        barbados_forms = BarbadosForm.objects.filter(order__status="criminal_check_passed")

        if not barbados_forms.exists():
            logging.info("No Barbados form foudn with the criminal check status of 'passed'.")

            # get random order to test
            test_order = order.objects.all()
            if not test_order.exists():
                print("No orders found to test.")
                return False
            
            # use the first order for testing 
            test_order = test_order.first()
            print(f"using test order: {test_order.id} - {test_order.first_name} {test_order.surname}")

            # get associated with BarbadosForm 
            try:
                barbados_form = BarbadosForm.objects.get(order=test_order)
            except BarbadosForm.DoesNotExist:
                print("No Barbados form found for this order.")
                return False
        
        else:
            # Get the first matching form
            barbados_form = barbados_forms.first()

        print(f"Found form for {barbados_form.order.first_name} {barbados_form.order.surname}")

        # Create and run the bot
        bot = BarbadosFormBot(barbados_form)
        result = bot.run()

        if result:
            print("Bot ran successfully.")
        else:
            print("Bot failed to run.")
        return result
    
    except Exception as e:
        print(f"error setting up test; {e}")
        print(traceback.format_exc())
        return False
    

# For use in signals.py - maintains the same interface as the original
def run_barbados_bot(form_data_instance):
    """Main entry point to run the Barbados bot - maintains compatibility with original."""
    bot = BarbadosFormBot(form_data_instance)
    return bot.run()


if __name__ == "__main__":
    test_with_real_data()