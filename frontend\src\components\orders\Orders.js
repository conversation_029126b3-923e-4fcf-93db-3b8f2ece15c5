import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import {
    Container,
    Typography,
    Alert,
    // Button, // No longer directly needed from MUI here
    CircularProgress,
    Box,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper
} from '@mui/material';
import CustomButton from '../shared/Custom_button'; // Import CustomButton
import { dateOptions } from '../../constants';
// Assuming useApiService is in the correct path
// import { useApiService } from '../../hooks/useApiService'; 

function Orders() {
    const location = useLocation();
    const navigate = useNavigate();

    const [message, setMessage] = useState(location.state?.message);
    const [errorMessage, setErrorMessage] = useState(null);
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);

    // Effect to clear the location state message after it's been read
    useEffect(() => {
        if (location.state?.message) {
            navigate(location.pathname, { replace: true, state: {} });
            const timer = setTimeout(() => {
                setMessage(null);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [location, navigate]);

    const fetchOrders = async () => {
        setLoading(true);
        setErrorMessage(null);
        try {
            const response = await fetch('/api/orders/', {
                credentials: 'include',
                headers: { 'Accept': 'application/json' }
            });
            if (!response.ok) {
                throw new Error(`Failed to fetch orders (Status: ${response.status})`);
            }
            const data = await response.json();
            setOrders(data.results || data);
        } catch (err) {
            console.error("Error fetching orders:", err);
            setErrorMessage(err.message);
            setOrders([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchOrders();
    }, []);

    return (
        <Container maxWidth={false} sx={{ mt: 4, mb: 4 }}>
            {/* Top Box: Only contains the title */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h4" component="h1">
                    Manage Orders
                </Typography>
                {/* "Add New Order" Button removed from here */}
            </Box>

            {message && (
                <Alert
                    severity="success"
                    onClose={() => setMessage(null)}
                    sx={{ mb: 2 }}
                >
                    {message}
                </Alert>
            )}

            {errorMessage && (
                <Alert
                    severity="error"
                    onClose={() => setErrorMessage(null)}
                    sx={{ mb: 2 }}
                >
                    {errorMessage}
                </Alert>
            )}

            {loading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                    <CircularProgress />
                </Box>
            )}

            {!loading && (
                <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
                    <Table sx={{ minWidth: 650 }} aria-label="orders table">
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>ID</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>First Name</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Surname</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Email</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Location</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Status</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Created At</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {orders.length === 0 && !errorMessage ? (
                                <TableRow>
                                    <TableCell colSpan={7} align="center">No orders found.</TableCell>
                                </TableRow>
                            ) : (
                                orders.map((order) => (
                                    <TableRow
                                        key={order.id}
                                        component={Link}
                                        to={`/orders/${order.id}/edit`}
                                        sx={{
                                            textDecoration: 'none',
                                            color: 'inherit',
                                            cursor: 'pointer',
                                            '&:hover': { backgroundColor: '#f5f5f5'}
                                        }}
                                    >
                                        <TableCell component="th" scope="row">{order.id}</TableCell>
                                        <TableCell>{order.first_name}</TableCell>
                                        <TableCell>{order.surname}</TableCell>
                                        <TableCell>{order.customer_email}</TableCell>
                                        <TableCell>{order.location_name || 'N/A'}</TableCell>
                                        <TableCell>{order.status}</TableCell>
                                        <TableCell>{new Date(order.created_at).toLocaleString('en-US', dateOptions) + ' (UTC)'}</TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>                            
            )}

            {/* Bottom Box: Only contains the "Add New Order" button, aligned to the right */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mt: 2, mb: 2 }}>
                <CustomButton
                    component={Link}
                    to="/orders/add"
                    variant="contained"
                    color="primary"
                    sx={{ p: 1.5 }}
                >
                    Add New Order
                </CustomButton>
            </Box>

        </Container>
    );
}

export default Orders;