{"openapi": "3.1.0", "info": {"title": "OpenSanctions API", "description": "\n## Introduction\n\nThe [OpenSanctions](https://www.opensanctions.org) API allows users to search and query\nthe OpenSanctions [entity graph](https://www.opensanctions.org/docs/entities/). The API\ncan be used to search people, companies and [other types of entities](https://www.opensanctions.org/reference/#schema).\nIn particular, it can be used to check the presence of a specific entity on [sanctions\nlists](https://www.opensanctions.org/datasets/sanctions/) or in the broader\n[OpenSanctions dataset](https://www.opensanctions.org/datasets/).\n\nSee also:\n* [Example code in Python, JS, and more](https://github.com/opensanctions/api-examples#folders-and-files)\n* [OpenSanctions API product page](https://www.opensanctions.org/docs/api/)\n* [Account and usage information](https://www.opensanctions.org/service/account/)\n* [API frequently asked questions](https://www.opensanctions.org/docs/api/faq/)\n\n### Which endpoint should I use?\n\nThe API supports a variety of methods to access and query OpenSanctions data:\n\n* **Entity matching ([/match](#tag/Matching/operation/match_match__dataset__post))**. Use this \n  endpoint if you have a set of people or companies you wish to check against\n  sanctions or PEPs lists. This API supports fuzzy name matching and using additional\n  criteria (e.g. nationality, birth dates, tax identifiers, or addresses) to [score\n  entity matches](https://www.opensanctions.org/matcher/) and avoid false positives.\n\n  [Read the tutorial](https://www.opensanctions.org/docs/api/matching/) for an introduction in how to use the matching API. You will also want to familiarise yourself with the [entity data model](https://www.opensanctions.org/docs/entities/) used by the system and the [data dictionary](https://www.opensanctions.org/reference/) entity\n  types and properties.\n\n* **Text-based search ([/search](#tag/Matching/operation/search_search__dataset__get))**\n  is a useful tool for providing a user-facing search interface, including faceted\n  navigation. The search uses [industry standard string syntax](https://www.opensanctions.org/docs/api/search/)\n  which can be used to perform field-specific searches, wildcard and fuzzy searches. The\n  scores and ranking are generated by the search index and express search relevance, not\n  match quality.\n\n* **Entity fetching ([/entities](#tag/Data-access/operation/fetch_entity_entities__entity_id__get))**.\n  Retrieve the full details about an entity in the database. The full record will include\n  adjacent entities, such as company owners and subsidiaries, family members and associates\n  for PEPs, and detailed records for addresses and identification documents linked to\n  entities. This endpoint can be used to perform incremental traversal of the OpenSanctions\n  graph.\n\n  Read about the [entity data model](https://www.opensanctions.org/docs/entities/) and \n  consult the [data dictionary](https://www.opensanctions.org/reference/) to see all entity\n  types and their respective properties.\n\n* **Statement-based data access ([/statements](#tag/Data-access/operation/statements_statements_get))**\n  allows browsing OpenSanctions data in its most granular form. This can be useful to see\n  what source dataset a particular assertion (e.g. the birth date of a person) is sourced\n  from. Read about the [statement data model](https://www.opensanctions.org/docs/statements/).\n\n* **OpenRefine Reconciliation API ([/reconcile](#tag/Reconciliation))**. A specialised API\n  that lets OpenSanctions data be used by the [OpenRefine](https://openrefine.org/) data\n  cleaning tool. Read [the tutorial](https://www.opensanctions.org/articles/2022-01-10-openrefine-reconciliation/)\n  to understand how this can be used as an investigative tool.\n\n## Authorization and API keys\n\nTo use the OpenSanctions API, you need an API key. Please visit the [API product page](https://www.opensanctions.org/api/) or [contact us](https://www.opensanctions.org/contact/) to learn more. OpenSanctions will issue free API keys to users in academia, non-profit organizations and those working in for-profit journalism.\n\n* [Using API authentication](https://www.opensanctions.org/docs/api/authentication/)\n\n## Dataset scopes\n\nThe API provides a sophisticated mechanism for selecting the scope of the data\nthat sould be queried by a particular request - for example, you can run a name \nagainst all sanctions lists, but not the datasets listing PEPs. \n\nDatasets can be of two types: source datasets - e.g. `us_ofac_sdn` (the US main\nsanctions list) contain only entities from one source. These source datasets are\ngrouped into collections - e.g. [`sanctions`](https://www.opensanctions.org/datasets/sanctions/),\nwhich contains entities from all sanctions lists contained in the database, or\n[`peps`](https://www.opensanctions.org/datasets/peps/), which covers all politically\nexposed persons sourced from various origins. If you are unsure which dataset you\nwant to screen against, use [`default`](https://www.opensanctions.org/datasets/default/) (the full\nOpenSanctions dataset).\n\nThese dataset scopes can be applied to searches and mapping requests by selecting\nthe desired scope in the URL path of the query: `/search/sanctions` will search \nall sanctioned entities, while `/search/us_ofac_sdn` limits the search to only\nthose entities listed on the US list.\n\nPlease note that each indivdual entity (e.g. a sanctioned person) can be linked to\nmultiple datasets because it has been [de-duplicated](https://www.opensanctions.org/docs/identifiers/).\n\n* See also: [List of datasets in OpenSanctions](https://www.opensanctions.org/datasets/)", "termsOfService": "https://www.opensanctions.org/docs/terms", "contact": {"name": "OpenSanctions", "url": "https://www.opensanctions.org/", "email": "<EMAIL>"}, "version": "yente 4.2.1", "x-logo": {"url": "https://assets.opensanctions.org/images/ura/logo_text_spaced.svg"}, "externalDocs": {"description": "Home page", "url": "https://www.opensanctions.org"}}, "paths": {"/match/{dataset}": {"post": {"tags": ["Matching"], "summary": "Query by example matcher", "description": "Match entities based on a complex set of criteria, like name, date of birth\nand nationality of a person. This works by submitting a batch of entities, each\nformatted like those returned by the API.\n\nTutorials:\n* [Using the matching API](https://www.opensanctions.org/docs/api/matching/)\n* [Configuring the scoring system](https://www.opensanctions.org/docs/api/scoring/)\n\nFor example, the following would be valid query examples:\n\n```json\n\"queries\": {\n    \"entity1\": {\n        \"schema\": \"Person\",\n        \"properties\": {\n            \"name\": [\"<PERSON>\"],\n            \"birthDate\": [\"1975-04-21\"],\n            \"nationality\": [\"us\"]\n        }\n    },\n    \"entity2\": {\n        \"schema\": \"Company\",\n        \"properties\": {\n            \"name\": [\"Brilliant Amazing Limited\"],\n            \"jurisdiction\": [\"hk\"],\n            \"registrationNumber\": [\"84BA99810\"]\n        }\n    }\n}\n```\nThe values for `entity1`, `entity2` can be chosen freely to correlate results\non the client side when the request is returned. The responses will be given\nfor each submitted example like this:\n\n```json\n\"responses\": {\n    \"entity1\": {\n        \"query\": {},\n        \"results\": [...]\n    },\n    \"entity2\": {\n        \"query\": {},\n        \"results\": [...]\n    }\n}\n```\n\nThe precision of the results will be dependent on the amount of detail submitted\nwith each example. The following properties are most helpful for particular types:\n\n* **Person**: ``name``, ``birthDate``, ``nationality``, ``idNumber``, ``address``\n* **Organization**: ``name``, ``country``, ``registrationNumber``, ``address``\n* **Company**: ``name``, ``jurisdiction``, ``registrationNumber``, ``address``,\n  ``incorporationDate``", "operationId": "match_match__dataset__post", "parameters": [{"name": "dataset", "in": "path", "required": true, "schema": {"type": "string", "description": "Data source or collection name to scope the query to.", "examples": ["default"], "title": "Dataset"}, "description": "Data source or collection name to scope the query to."}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "title": "Number of results to return", "default": 5}}, {"name": "threshold", "in": "query", "required": false, "schema": {"type": "number", "title": "Score threshold for results to be considered matches", "default": 0.7}}, {"name": "cutoff", "in": "query", "required": false, "schema": {"type": "number", "title": "Lower bound of score for results to be returned at all", "default": 0.5}}, {"name": "algorithm", "in": "query", "required": false, "schema": {"type": "string", "title": "Scoring algorithm to use, options: logic-v1, name-based, name-qualified, regression-v1, regression-v2 (best: logic-v1)", "default": "logic-v1"}}, {"name": "include_dataset", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Only include the given datasets in results", "default": []}}, {"name": "exclude_schema", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Remove the given types of entities from results", "default": []}}, {"name": "exclude_dataset", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Remove the given datasets from results", "default": []}}, {"name": "topics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Only return results that match the given topics", "default": []}}, {"name": "fuzzy", "in": "query", "required": false, "schema": {"type": "boolean", "title": "Use slow matching for candidate generation, does not affect scores", "deprecated": true, "default": true}, "deprecated": true}, {"name": "changed_since", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}(T\\d{2}(:\\d{2}(:\\d{2})?)?)?$"}, {"type": "null"}], "title": "Match against entities that were updated since the given date"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityMatchQuery"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityMatchResponse"}}}}, "400": {"description": "Invalid query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/search/{dataset}": {"get": {"tags": ["Matching"], "summary": "Simple entity search", "description": "Search endpoint for matching entities based on a simple piece of text, e.g.\na name. This can be used to implement a simple, user-facing search. For proper\nentity matching, the multi-property matching API should be used instead.\n\nSearch queries can include field-specific fitlers, wildcards and fuzzy searches.\nSee also: [search API documentation](https://www.opensanctions.org/docs/api/search/).", "operationId": "search_search__dataset__get", "parameters": [{"name": "dataset", "in": "path", "required": true, "schema": {"type": "string", "description": "Data source or collection name to scope the query to.", "examples": ["default"], "title": "Dataset"}, "description": "Data source or collection name to scope the query to."}, {"name": "q", "in": "query", "required": false, "schema": {"type": "string", "title": "Query text", "default": ""}}, {"name": "schema", "in": "query", "required": false, "schema": {"type": "string", "title": "Types of entities that can match the search", "default": "Thing"}}, {"name": "include_dataset", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Restrict the search scope to datasets (that are in the given scope) to search entities within.", "description": "Limit the results to entities that are part of at least one of the given datasets.", "default": []}, "description": "Limit the results to entities that are part of at least one of the given datasets."}, {"name": "exclude_dataset", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Remove specific datasets (that are in the given scope) from the search scope.", "default": []}}, {"name": "exclude_schema", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Remove the given types of entities from results", "default": []}}, {"name": "changed_since", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}(T\\d{2}(:\\d{2}(:\\d{2})?)?)?$"}, {"type": "null"}], "title": "Search entities that were updated since the given date"}}, {"name": "countries", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter by country codes", "default": []}}, {"name": "topics", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter by entity topics (e.g. sanction, role.pep)", "default": []}}, {"name": "datasets", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Filter by dataset names, for faceting use (respects operator choice).", "default": []}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "title": "Number of results to return", "default": 10}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 9499, "title": "Start at result with given offset", "default": 0}}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Sorting criteria", "default": []}}, {"name": "target", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Include only targeted entities", "description": "Please specify a list of topics of concern, instead.", "deprecated": true}, "description": "Please specify a list of topics of concern, instead.", "deprecated": true}, {"name": "fuzzy", "in": "query", "required": false, "schema": {"type": "boolean", "title": "Allow fuzzy query syntax", "default": false}}, {"name": "simple", "in": "query", "required": false, "schema": {"type": "boolean", "title": "Use simple syntax for user-facing query boxes", "default": false}}, {"name": "facets", "in": "query", "required": false, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Facet"}, "title": "Facet counts to include in response.", "default": ["countries", "topics", "datasets"]}}, {"name": "filter_op", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/Operator", "title": "Define behaviour of multiple filters on one field", "description": "Logic to use when combining multiple filters on the same field (topics, countries, datasets). Please specify AND for new integrations (to override a legacy default) and when building a faceted user interface.", "default": "OR"}, "description": "Logic to use when combining multiple filters on the same field (topics, countries, datasets). Please specify AND for new integrations (to override a legacy default) and when building a faceted user interface."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponse"}}}}, "400": {"description": "Invalid query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/entities/{entity_id}": {"get": {"tags": ["Data access"], "summary": "Fetch Entity", "description": "Retrieve a single entity by its ID. The entity will be returned in\nfull, with data from all datasets and with nested entities (adjacent\npassport, sanction and associated entities) included. If the entity ID\nhas been merged into a different canonical entity, an HTTP redirect will\nbe triggered.\n\nIntro: [entity data model](https://www.opensanctions.org/docs/entities/).", "operationId": "fetch_entity_entities__entity_id__get", "parameters": [{"name": "entity_id", "in": "path", "required": true, "schema": {"type": "string", "description": "ID of the entity to retrieve", "examples": ["Q7747"], "title": "Entity Id"}, "description": "ID of the entity to retrieve"}, {"name": "nested", "in": "query", "required": false, "schema": {"type": "boolean", "title": "Include adjacent entities (e.g. addresses, family) in response", "default": true}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityResponse"}}}}, "307": {"description": "The entity was merged into another ID"}, "404": {"description": "Entity not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/reconcile/{dataset}": {"get": {"tags": ["Reconciliation"], "summary": "Reconciliation manifest", "description": "Reconciliation API, emulates Google Refine API. This endpoint can be used\nto bulk match entities against the system using an end-user application like\n[OpenRefine](https://openrefine.org). The reconciliation API uses the same\nsearch and matching functions as the matching API and will also produce\nscores that reflect additional properties like country or date of birth, if\nspecified.\n\nTutorial: [Using OpenRefine to match entities in a spreadsheet](https://www.opensanctions.org/articles/2022-01-10-openrefine-reconciliation/).", "operationId": "reconcile_reconcile__dataset__get", "parameters": [{"name": "dataset", "in": "path", "required": true, "schema": {"type": "string", "description": "Data source or collection name to scope the query to.", "examples": ["default"], "title": "Dataset"}, "description": "Data source or collection name to scope the query to."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FreebaseManifest"}}}}, "400": {"description": "Invalid query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/catalog": {"get": {"tags": ["Data access"], "summary": "Data catalog", "description": "Return the service manifest, which includes a list of all indexed datasets.\n\nThe manifest is the configuration file of the yente service. It specifies what\ndata sources are included, and how often they should be loaded.", "operationId": "catalog_catalog_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataCatalogModel"}}}}}}}, "/algorithms": {"get": {"tags": ["System information"], "summary": "Algorithms", "description": "Return a list of the supported matching/scoring algorithms used by the matching\nendpoint.\n\nSee also the [scoring documentation](https://www.opensanctions.org/docs/api/scoring/).", "operationId": "algorithms_algorithms_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlgorithmResponse"}}}}}}}, "/billing/account": {"get": {"tags": ["System information"], "summary": "Retrieve account details", "description": "Return the account details linked to the given API key and the\nmeasured API request usage of the account in the past period.", "operationId": "get_account_billing_account_get", "parameters": [{"name": "usage_credential", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Usage Credential"}}, {"name": "access_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "OAuth access token"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountResponse"}}}}, "403": {"description": "Authorization error.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/customers/{customer_id}/users/": {"post": {"summary": "Create User", "operationId": "create_user_customers__customer_id__users__post", "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Customer Id"}}, {"name": "access_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "OAuth access token"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/statements": {"get": {"tags": ["Data access"], "summary": "Statement-based records", "description": "Access raw entity data as statements.\n\nRead [statement-based data model](https://www.opensanctions.org/docs/statements/)\nfor context regarding this endpoint.\n\nUsing this endpoint does not incur a charge, but a valid API key must be supplied.", "operationId": "statements_statements_get", "parameters": [{"name": "dataset", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter by dataset"}}, {"name": "entity_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter by source entity ID"}}, {"name": "canonical_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter by normalised entity ID"}}, {"name": "prop", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter by property name"}}, {"name": "value", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter by property value"}}, {"name": "schema", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter by schema type"}}, {"name": "sort", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Sorting criteria", "default": ["canonical_id", "prop"]}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 5000, "title": "Number of results to return", "default": 50}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 9499, "title": "Number of results to skip before returning them", "default": 0}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AccountResponse": {"properties": {"credentials": {"items": {"$ref": "#/components/schemas/Credential"}, "type": "array", "title": "Credentials"}, "user": {"$ref": "#/components/schemas/User"}, "customer": {"$ref": "#/components/schemas/Customer"}, "usage": {"$ref": "#/components/schemas/UserUsage"}}, "type": "object", "required": ["credentials", "user", "customer", "usage"], "title": "AccountResponse"}, "Algorithm": {"properties": {"name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "features": {"additionalProperties": {"$ref": "#/components/schemas/FeatureDoc"}, "type": "object", "title": "Features"}}, "type": "object", "required": ["name", "features"], "title": "Algorithm"}, "AlgorithmResponse": {"properties": {"algorithms": {"items": {"$ref": "#/components/schemas/Algorithm"}, "type": "array", "title": "Algorithms"}, "default": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "best": {"type": "string", "title": "Best"}}, "type": "object", "required": ["algorithms", "default", "best"], "title": "AlgorithmResponse"}, "Credential": {"properties": {"id": {"type": "integer", "title": "Id"}, "customer_id": {"type": "integer", "title": "Customer Id"}, "label": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Label"}, "secret": {"type": "string", "title": "Secret"}, "chargebee_subscription_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Chargebee Subscription Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At"}, "monthly_quota": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Monthly Quota"}}, "type": "object", "required": ["id", "customer_id", "label", "secret", "chargebee_subscription_id", "created_at", "expires_at", "monthly_quota"], "title": "Credential"}, "Customer": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "chargebee_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Chargebee Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "name", "chargebee_id", "created_at"], "title": "Customer"}, "DataCatalogModel": {"properties": {"datasets": {"items": {"$ref": "#/components/schemas/DatasetModel"}, "type": "array", "title": "Datasets"}, "current": {"items": {"type": "string"}, "type": "array", "title": "Current"}, "outdated": {"items": {"type": "string"}, "type": "array", "title": "Outdated"}, "index_stale": {"type": "boolean", "title": "Index Stale", "default": false}}, "type": "object", "required": ["datasets", "current", "outdated"], "title": "DataCatalogModel"}, "DatasetModel": {"properties": {"name": {"type": "string", "title": "Name"}, "title": {"type": "string", "title": "Title"}, "summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Summary"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url"}, "load": {"type": "boolean", "title": "Load"}, "entities_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entities Url"}, "version": {"type": "string", "title": "Version"}, "index_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Index Version"}, "index_current": {"type": "boolean", "title": "Index Current", "default": false}, "children": {"items": {"type": "string"}, "type": "array", "title": "Children"}}, "type": "object", "required": ["name", "title", "load", "version", "children"], "title": "DatasetModel"}, "DateUsage": {"properties": {"routes": {"items": {"$ref": "#/components/schemas/RouteUsage"}, "type": "array", "title": "Routes"}, "date": {"type": "string", "format": "date", "title": "Date"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["routes", "date", "total"], "title": "DateUsage"}, "EntityExample": {"properties": {"id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id", "examples": ["my-entity-id"]}, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "examples": ["Person"]}, "properties": {"additionalProperties": {"anyOf": [{"type": "string"}, {"items": {}, "type": "array"}]}, "type": "object", "title": "Properties", "examples": [{"name": ["<PERSON>"]}]}}, "type": "object", "required": ["schema", "properties"], "title": "EntityExample"}, "EntityMatchQuery": {"properties": {"weights": {"additionalProperties": {"type": "number"}, "type": "object", "title": "Weights", "default": {}, "examples": [{"name_literal": 0.8}]}, "queries": {"additionalProperties": {"$ref": "#/components/schemas/EntityExample"}, "type": "object", "title": "Queries"}}, "type": "object", "required": ["queries"], "title": "EntityMatchQuery"}, "EntityMatchResponse": {"properties": {"responses": {"additionalProperties": {"$ref": "#/components/schemas/EntityMatches"}, "type": "object", "title": "Responses"}, "matcher": {"additionalProperties": {"$ref": "#/components/schemas/FeatureDoc"}, "type": "object", "title": "Matcher"}, "limit": {"type": "integer", "title": "Limit", "examples": [5]}}, "type": "object", "required": ["responses", "matcher", "limit"], "title": "EntityMatchResponse"}, "EntityMatches": {"properties": {"status": {"type": "integer", "title": "Status", "default": 200, "examples": [200]}, "results": {"items": {"$ref": "#/components/schemas/ScoredEntityResponse"}, "type": "array", "title": "Results"}, "total": {"$ref": "#/components/schemas/TotalSpec"}, "query": {"$ref": "#/components/schemas/EntityExample"}}, "type": "object", "required": ["results", "total", "query"], "title": "EntityMatches"}, "EntityResponse": {"properties": {"id": {"type": "string", "title": "Id", "examples": ["NK-A7z...."]}, "caption": {"type": "string", "title": "Caption", "examples": ["<PERSON>"]}, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "examples": ["LegalEntity"]}, "properties": {"additionalProperties": {"items": {"anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/EntityResponse"}]}, "type": "array"}, "type": "object", "title": "Properties", "examples": [{"name": ["<PERSON>"]}]}, "datasets": {"items": {"type": "string"}, "type": "array", "title": "Datasets", "default": [], "examples": [["us_ofac_sdn"]]}, "referents": {"items": {"type": "string"}, "type": "array", "title": "Referents", "default": [], "examples": [["ofac-1234"]]}, "target": {"type": "boolean", "title": "Target", "default": false}, "first_seen": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Seen", "examples": ["2025-02-27T14:19:59.541719Z"]}, "last_seen": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Seen", "examples": ["2025-02-27T14:19:59.541719Z"]}, "last_change": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Change", "examples": ["2025-02-27T14:19:59.541719Z"]}}, "type": "object", "required": ["id", "caption", "schema", "properties"], "title": "EntityResponse"}, "ErrorResponse": {"properties": {"detail": {"type": "string", "title": "Detail", "examples": ["Detailed error message"]}}, "type": "object", "required": ["detail"], "title": "ErrorResponse"}, "Facet": {"type": "string", "enum": ["datasets", "schema", "countries", "identifiers", "topics", "genders"], "title": "Facet"}, "FeatureDoc": {"properties": {"description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "coefficient": {"type": "number", "title": "Coefficient"}, "url": {"type": "string", "title": "Url"}}, "type": "object", "required": ["description", "coefficient", "url"], "title": "FeatureDoc", "description": "Documentation for a particular feature in the matching API model."}, "FreebaseManifest": {"properties": {"versions": {"items": {"type": "string"}, "type": "array", "title": "Versions", "examples": [["0.2"]]}, "name": {"type": "string", "title": "Name", "examples": ["yente"]}, "identifierSpace": {"type": "string", "minLength": 1, "format": "uri", "title": "Identifierspace"}, "schemaSpace": {"type": "string", "minLength": 1, "format": "uri", "title": "Schemaspace"}, "documentation": {"type": "string", "minLength": 1, "format": "uri", "title": "Documentation"}, "batchSize": {"type": "integer", "title": "Batchsize"}, "view": {"$ref": "#/components/schemas/FreebaseManifestView"}, "preview": {"$ref": "#/components/schemas/FreebaseManifestPreview"}, "suggest": {"$ref": "#/components/schemas/FreebaseManifestSuggest"}, "extend": {"$ref": "#/components/schemas/FreebaseManifestExtend"}, "defaultTypes": {"items": {"$ref": "#/components/schemas/FreebaseType"}, "type": "array", "title": "Defaulttypes"}}, "type": "object", "required": ["versions", "name", "identifierSpace", "schemaSpace", "documentation", "batchSize", "view", "preview", "suggest", "extend", "defaultTypes"], "title": "FreebaseManifest"}, "FreebaseManifestExtend": {"properties": {"propose_properties": {"$ref": "#/components/schemas/FreebaseManifestExtendProposeProperties"}, "propose_settings": {"items": {"$ref": "#/components/schemas/FreebaseManifestExtendPropertySetting"}, "type": "array", "title": "Propose Settings"}}, "type": "object", "required": ["propose_properties", "propose_settings"], "title": "FreebaseManifestExtend"}, "FreebaseManifestExtendPropertySetting": {"properties": {"name": {"type": "string", "title": "Name"}, "label": {"type": "string", "title": "Label"}, "type": {"type": "string", "title": "Type"}, "default": {"title": "<PERSON><PERSON><PERSON>"}, "help_text": {"type": "string", "title": "Help Text"}, "choices": {"items": {"$ref": "#/components/schemas/FreebaseManifestExtendPropertySettingChoice"}, "type": "array", "title": "Choices", "default": []}}, "type": "object", "required": ["name", "label", "type", "default", "help_text"], "title": "FreebaseManifestExtendPropertySetting"}, "FreebaseManifestExtendPropertySettingChoice": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}}, "type": "object", "required": ["id", "name"], "title": "FreebaseManifestExtendPropertySettingChoice"}, "FreebaseManifestExtendProposeProperties": {"properties": {"service_url": {"type": "string", "minLength": 1, "format": "uri", "title": "Service Url"}, "service_path": {"type": "string", "title": "Service Path"}}, "type": "object", "required": ["service_url", "service_path"], "title": "FreebaseManifestExtendProposeProperties"}, "FreebaseManifestPreview": {"properties": {"url": {"type": "string", "title": "Url"}, "width": {"type": "integer", "title": "<PERSON><PERSON><PERSON>"}, "height": {"type": "integer", "title": "Height"}}, "type": "object", "required": ["url", "width", "height"], "title": "FreebaseManifestPreview"}, "FreebaseManifestSuggest": {"properties": {"entity": {"$ref": "#/components/schemas/FreebaseManifestSuggestType"}, "type": {"$ref": "#/components/schemas/FreebaseManifestSuggestType"}, "property": {"$ref": "#/components/schemas/FreebaseManifestSuggestType"}}, "type": "object", "required": ["entity", "type", "property"], "title": "FreebaseManifestSuggest"}, "FreebaseManifestSuggestType": {"properties": {"service_url": {"type": "string", "minLength": 1, "format": "uri", "title": "Service Url"}, "service_path": {"type": "string", "title": "Service Path"}}, "type": "object", "required": ["service_url", "service_path"], "title": "FreebaseManifestSuggestType"}, "FreebaseManifestView": {"properties": {"url": {"type": "string", "title": "Url"}}, "type": "object", "required": ["url"], "title": "FreebaseManifestView"}, "FreebaseType": {"properties": {"id": {"type": "string", "title": "Id", "examples": ["Person"]}, "name": {"type": "string", "title": "Name", "examples": ["People"]}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["id", "name"], "title": "FreebaseType"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Operator": {"type": "string", "enum": ["AND", "OR"], "title": "Operator"}, "RouteUsage": {"properties": {"route": {"type": "string", "title": "Route"}, "count": {"type": "integer", "title": "Count"}}, "type": "object", "required": ["route", "count"], "title": "RouteUsage"}, "ScoredEntityResponse": {"properties": {"id": {"type": "string", "title": "Id", "examples": ["NK-A7z...."]}, "caption": {"type": "string", "title": "Caption", "examples": ["<PERSON>"]}, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "examples": ["LegalEntity"]}, "properties": {"additionalProperties": {"items": {"anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/EntityResponse"}]}, "type": "array"}, "type": "object", "title": "Properties", "examples": [{"name": ["<PERSON>"]}]}, "datasets": {"items": {"type": "string"}, "type": "array", "title": "Datasets", "default": [], "examples": [["us_ofac_sdn"]]}, "referents": {"items": {"type": "string"}, "type": "array", "title": "Referents", "default": [], "examples": [["ofac-1234"]]}, "target": {"type": "boolean", "title": "Target", "default": false}, "first_seen": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Seen", "examples": ["2025-02-27T14:19:59.541719Z"]}, "last_seen": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Seen", "examples": ["2025-02-27T14:19:59.541719Z"]}, "last_change": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Change", "examples": ["2025-02-27T14:19:59.541719Z"]}, "score": {"type": "number", "title": "Score", "default": 0.99}, "features": {"additionalProperties": {"type": "number"}, "type": "object", "title": "Features"}, "match": {"type": "boolean", "title": "Match", "default": false}}, "type": "object", "required": ["id", "caption", "schema", "properties", "features"], "title": "ScoredEntityResponse"}, "SearchFacet": {"properties": {"label": {"type": "string", "title": "Label", "examples": ["Countries"]}, "values": {"items": {"$ref": "#/components/schemas/SearchFacetItem"}, "type": "array", "title": "Values"}}, "type": "object", "required": ["label", "values"], "title": "SearchFacet"}, "SearchFacetItem": {"properties": {"name": {"type": "string", "title": "Name", "examples": ["ru"]}, "label": {"type": "string", "title": "Label", "examples": ["Russia"]}, "count": {"type": "integer", "title": "Count", "default": 1, "examples": [42]}}, "type": "object", "required": ["name", "label"], "title": "SearchFacetItem"}, "SearchResponse": {"properties": {"limit": {"type": "integer", "title": "Limit", "examples": [20]}, "offset": {"type": "integer", "title": "Offset", "default": 0, "examples": [0]}, "total": {"$ref": "#/components/schemas/TotalSpec"}, "results": {"items": {"$ref": "#/components/schemas/EntityResponse"}, "type": "array", "title": "Results"}, "facets": {"additionalProperties": {"$ref": "#/components/schemas/SearchFacet"}, "type": "object", "title": "Facets"}}, "type": "object", "required": ["limit", "total", "results", "facets"], "title": "SearchResponse"}, "Statement": {"properties": {"id": {"type": "string", "title": "Id", "examples": ["0000ad52d4d91a8..."]}, "entity_id": {"type": "string", "title": "Entity Id", "examples": ["ofac-1234"]}, "canonical_id": {"type": "string", "title": "Canonical Id", "examples": ["NK-1234"]}, "prop": {"type": "string", "title": "Prop", "examples": ["alias"]}, "prop_type": {"type": "string", "title": "Prop Type", "examples": ["name"]}, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "examples": ["LegalEntity"]}, "value": {"type": "string", "title": "Value", "examples": ["<PERSON>"]}, "original_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Original Value", "examples": ["<PERSON> (Esq.)"]}, "dataset": {"type": "string", "title": "Dataset", "examples": ["us_ofac_sdn"]}, "lang": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>", "examples": ["eng"]}, "first_seen": {"type": "string", "format": "date-time", "title": "First Seen"}, "last_seen": {"type": "string", "format": "date-time", "title": "Last Seen"}}, "type": "object", "required": ["id", "entity_id", "canonical_id", "prop", "prop_type", "schema", "value", "dataset", "first_seen", "last_seen"], "title": "Statement"}, "StatementResponse": {"properties": {"limit": {"type": "integer", "title": "Limit", "examples": [20]}, "offset": {"type": "integer", "title": "Offset", "default": 0, "examples": [0]}, "total": {"$ref": "#/components/schemas/TotalSpec"}, "results": {"items": {"$ref": "#/components/schemas/Statement"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["limit", "total", "results"], "title": "StatementResponse"}, "TotalSpec": {"properties": {"value": {"type": "integer", "title": "Value", "examples": [42]}, "relation": {"type": "string", "title": "Relation", "default": "eq", "examples": ["eq"]}}, "type": "object", "required": ["value"], "title": "TotalSpec"}, "User": {"properties": {"id": {"type": "integer", "title": "Id"}, "customer_id": {"type": "integer", "title": "Customer Id"}, "sub": {"type": "string", "title": "Sub"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "email": {"type": "string", "title": "Email"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "blocked": {"type": "boolean", "title": "Blocked"}}, "type": "object", "required": ["id", "customer_id", "sub", "name", "email", "created_at", "blocked"], "title": "User"}, "UserCreateRequest": {"properties": {"email": {"type": "string", "title": "Email"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}}, "type": "object", "required": ["email"], "title": "UserCreateRequest"}, "UserUsage": {"properties": {"dates": {"items": {"$ref": "#/components/schemas/DateUsage"}, "type": "array", "title": "Dates"}, "days": {"type": "integer", "title": "Days"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["dates", "days", "total"], "title": "UserUsage"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}, "tags": [{"name": "Matching", "description": "Endpoints for conducting a user-facing entity search or matching a local data store against the given dataset.", "externalDocs": {"description": "Data dictionary", "url": "https://opensanctions.org/reference/"}}, {"name": "System information", "description": "Service metadata endpoints for health checking and getting the application metadata to be used in client applications."}, {"name": "Data access", "description": "Endpoints for fetching data from the API, either related to individual entities, or for bulk data access in various forms.", "externalDocs": {"description": "Data dictionary", "url": "https://opensanctions.org/reference/"}}, {"name": "Reconciliation", "description": "The Reconciliation Service provides four separate endpoints that work in concert to implement the data matching API used by OpenRefine, Wikidata and several other services and utilities.", "externalDocs": {"description": "W3C Community API specification", "url": "https://reconciliation-api.github.io/specs/latest/"}}]}