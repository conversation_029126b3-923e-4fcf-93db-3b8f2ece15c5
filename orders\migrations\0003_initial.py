# Generated by Django 5.1.6 on 2025-02-12 23:38

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("locations", "0001_initial"),
        ("orders", "0002_delete_location"),
    ]

    operations = [
        migrations.CreateModel(
            name="Order",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("customer_name", models.CharField(max_length=100)),
                ("customer_email", models.CharField(max_length=100)),
                ("qr_code", models.TextField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("qr_generated", "QR Generated"),
                            ("criminal_check_passed", "Criminal Check Passed"),
                            ("criminal_check_failed", "Criminal Check Failed"),
                            ("refunded", "Refunded"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        max_length=25,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="locations.location",
                    ),
                ),
            ],
        ),
    ]
