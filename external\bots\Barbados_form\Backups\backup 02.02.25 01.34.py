from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, NoSuchElementException
from selenium.webdriver.support.ui import Select

import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime 
import logging






# Verification functions
def verify_radio_selection(driver, xpath, expected_value):
    try:
        # Print all radio buttons on the page
        all_radio_buttons = driver.find_elements(By.XPATH, "//input[@type='radio']")
        logging.info(f"Found {len(all_radio_buttons)} radio buttons on the page")
        for i, rb in enumerate(all_radio_buttons):
            # Get the sibling label for each radio button
            label = rb.find_element(By.XPATH, "following-sibling::label")
            logging.info(f"Radio button {i}: Label text: '{label.text}', Selected: {rb.is_selected()}")

        # Look for the label with the expected text, then get its preceding sibling input
        selected_radio = driver.find_element(
            By.XPATH,
            f"//label[contains(., '{expected_value}')]/preceding-sibling::input[@type='radio']"
        )
        if selected_radio.is_selected():
            logging.info(f"Successfully selected: {expected_value}")
            return True
        else:
            logging.warning(f"Failed to select {expected_value}. Radio button is not selected.")
            return False
    except Exception as e:
        logging.error(f"Error verifying radio selection: {e}")
        return False

def verify_input_field(driver, xpath, expected_value):

    try:
        element = driver.find_element(By.XPATH, xpath)
        current_value = element.get_attribute("value")
        logging.info(f"Verification: found value '{current_value}', expected '{expected_value}'")
        return current_value == expected_value
    except Exception as e:
        logging.error(f"Error during field verification: {e}")
        return False






# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Set up Google Sheets API
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_name('credentials.json', scope)
client = gspread.authorize(creds)

# Access the spreadsheet using its ID
sheet = client.open_by_key('1x_Mw6grKUerIIZGjh2isnm2Wh55fSNyu4vzXBCyXtNM')
worksheet = sheet.worksheet('TEST AUTOMATION')  # Get the worksheet by title

# Get all values including headers
all_values = worksheet.get_all_values()

# Print the headers to see what we're dealing with
logging.info("Current headers:")
logging.info(all_values[0])  # First row contains headers

# Get the data with headers
headers = all_values[0]
data = all_values[1:]  # All rows except the header
df = pd.DataFrame(data, columns=headers)

# Print first few rows to verify data loading
logging.info("\nLoaded data preview:")
logging.info(df.head())

service = Service(executable_path="chromedriver.exe")
driver = webdriver.Chrome(service=service)

wait = WebDriverWait(driver, 10)

driver.get("https://www.travelform.gov.bb/create")
logging.info("Navigated to the website")













# ----------------------------------------------------------------STAGE 1: ARRIVAL----------------------------------------------------------------

# [--WHAT IS YOUR RESIDENTIAL STATUS IN BARBADOS?--]
try:
    residential_status = df.loc[0, 'What is your residential Status in Barbados']
    logging.info(f"Residential status from sheet: {residential_status}")
except KeyError:
    logging.error("Column 'What is your residential Status in Barbados' not found in DataFrame")
    logging.error("Available columns:", df.columns)
    driver.quit()
    exit()

time.sleep(1)

try:
    if residential_status == 'Non-Resident/Visitor':
        status_option = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//label[.//span[text()='Non-Resident/Visitor']]"))
        )
    elif residential_status == 'Permanent Address Is In Barbados':
        status_option = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//label[.//span[text()='Permanent Address Is In Barbados']]"))
        )
    else:
        logging.error(f"Unrecognized residential status: {residential_status}")
        driver.quit()
        exit()

    # Scroll the element into view
    driver.execute_script("arguments[0].scrollIntoView(true);", status_option)
    time.sleep(0.5)  # Give the page a moment to settle after scrolling

    status_option.click()
    logging.info(f"Clicked residential status: {residential_status}")

    # Verify selection
    if not verify_radio_selection(driver, "//div[@data-path='arrival.residentStatus']//label", residential_status):
        logging.error("Failed to select correct residential status")
        driver.quit()
        exit()

except TimeoutException:
    logging.error(f"Timeout waiting for residential status option: {residential_status}")
    driver.quit()
    exit()
except NoSuchElementException:
    logging.error(f"Could not find residential status option: {residential_status}")
    driver.quit()
    exit()
except Exception as e:
    logging.error(f"Unexpected error selecting residential status: {e}")
    driver.quit()
    exit()





# [--HOW ARE YOU ENTERING BARBADOS?--]

# Get the entry method from the spreadsheet
try:
    entry_method = df.loc[0, 'How are you entering Barbados?']
    logging.info(f"Entry method from sheet: {entry_method}")
except KeyError:
    logging.error("Column 'How are you entering Barbados?' not found in DataFrame")
    logging.error(f"Available columns: {df.columns}")
    driver.quit()
    exit()

time.sleep(1)

try:
    if entry_method == 'Air':
        method_option = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//label[.//span[text()='Air']]"))
        )
    elif entry_method == 'Sea':
        method_option = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//label[.//span[text()='Sea']]"))
        )
    else:
        logging.error(f"Unrecognized entry method: {entry_method}")
        driver.quit()
        exit()


    method_option.click()
    logging.info(f"Clicked entry method: {entry_method}")

    # Verify selection
    if not verify_radio_selection(driver, "//div[@data-path='arrival.carrierType']//label", entry_method):
        logging.error("Failed to select correct entry method")
        driver.quit()
        exit()

except TimeoutException:
    logging.error(f"Timeout waiting for entry method option: {entry_method}")
    driver.quit()
    exit()
except NoSuchElementException:
    logging.error(f"Could not find entry method option: {entry_method}")
    driver.quit()
    exit()
except Exception as e:
    logging.error(f"Unexpected error selecting entry method: {e}")
    driver.quit()
    exit()




# [--AIRLINE--]

time.sleep(1)

# Get the airline name from the spreadsheet
try:
    airline = df.loc[0, 'Airline']
    logging.info(f"Airline from sheet: {airline}")
except KeyError:
    logging.error("Column 'Airline' not found in DataFrame")
    logging.error(f"Available columns: {df.columns}")
    driver.quit()
    exit()

time.sleep(1)

# Click the dropdown
try:
    dropdown_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.carrierName']"))
    )
    dropdown_element.click()
    logging.info("Dropdown clicked")
except TimeoutException:
    logging.error("Dropdown element not found")
    driver.quit()
    exit()

time.sleep(1)

# Type the airline name
try:
    dropdown_element.send_keys(airline)
    logging.info(f"Typed airline name: {airline}")
except Exception as e:
    logging.error(f"Failed to type airline name: {e}")
    driver.quit()
    exit()

time.sleep(0.3)

# Press Down followed by Enter to select the airline
try:
    dropdown_element.send_keys(Keys.DOWN, Keys.RETURN)
    logging.info("Pressed Enter to select airline")
except Exception as e:
    logging.error(f"Failed to press Enter: {e}")
    driver.quit()
    exit()

# Verify the selection using the helper function
try:
    if not verify_input_field(driver, "//*[@data-path='arrival.carrierName']", airline):
        logging.error("Failed to verify correct airline selection")
        driver.quit()
        exit()
except Exception as e:
    logging.error(f"Error during airline verification: {e}")
    driver.quit()
    exit()


# [--FLIGHT REGISTRATION NUMBER--]

# Get the flight registration number from the spreadsheet
try:
    flight_registration_number = df.loc[0, 'Flight Registration Number']
    logging.info(f"Flight Registration Number from sheet: {flight_registration_number}")
except KeyError:
    logging.error("Column 'Flight Registration Number' not found in DataFrame")
    logging.error(f"Available columns: {df.columns}")
    driver.quit()
    exit()

time.sleep(1)

# Locate the flight registration input field, clear it, and type in the value
try:
    flight_registration_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.carrierID']"))
    )
    flight_registration_element.clear()
    flight_registration_element.send_keys(flight_registration_number)
    logging.info(f"Typed flight registration number: {flight_registration_number}")
except TimeoutException:
    logging.error("Flight registration number element not found")
    driver.quit()
    exit()

time.sleep(0.3)

# Verify the flight registration number was correctly entered (uses the verify_input_field helper)
try:
    if not verify_input_field(driver, "//*[@data-path='arrival.carrierID']", flight_registration_number):
        logging.error("Failed to verify flight registration number")
        driver.quit()
        exit()
except Exception as e:
    logging.error(f"Error during flight registration number verification: {e}")
    driver.quit()
    exit()


# [--COUNTRY OF EMBARKATION --]

# Get the country of embarkation from the spreadsheet
try:
    country_of_embarkation = df.loc[0, 'Country of Embarkation']
    logging.info(f"Country of Embarkation from sheet: {country_of_embarkation}")
except KeyError:
    logging.error("Column 'Country of Embarkation' not found in DataFrame")
    logging.error(f"Available columns: {df.columns}")
    driver.quit()
    exit()

time.sleep(1)

# Click the dropdown
try:
    country_dropdown_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.embarkCountry']"))
    )
    country_dropdown_element.click()
    logging.info("Country dropdown clicked")
except TimeoutException:
    logging.error("Country dropdown element not found")
    driver.quit()
    exit()

time.sleep(1)

# Type the country of embarkation
try:
    country_dropdown_element.send_keys(country_of_embarkation)
    logging.info(f"Typed country of embarkation: {country_of_embarkation}")
except Exception as e:
    logging.error(f"Failed to type country of embarkation: {e}")
    driver.quit()
    exit()

time.sleep(0.3)

# Press Down followed by Enter to select the country of embarkation
try:
    country_dropdown_element.send_keys(Keys.DOWN, Keys.RETURN)
    logging.info("Pressed Enter to select country")
except Exception as e:
    logging.error(f"Failed to press Enter: {e}")
    driver.quit()
    exit()

# Verify the country of embarkation was correctly entered (uses the verify_input_field helper)
try:
    if not verify_input_field(driver, "//*[@data-path='arrival.embarkCountry']", country_of_embarkation):
        logging.error("Failed to verify country of embarkation")
        driver.quit()
        exit()
except Exception as e:
    logging.error(f"Error during country of embarkation verification: {e}")
    driver.quit()
    exit()


# [--PORT OF EMBARKATION--]

# Get the port of embarkation from the spreadsheet
try:
    port_of_embarkation = df.loc[0, 'Port of Embarkation']
    logging.info(f"Port of Embarkation from sheet: {port_of_embarkation}")
except KeyError:
    logging.error("Column 'Port of Embarkation' not found in DataFrame")
    logging.error(f"Available columns: {df.columns}")
    driver.quit()
    exit()

time.sleep(1)

# Click the dropdown
try:
    port_dropdown_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.embarkPort']"))
    )
    port_dropdown_element.click()
    logging.info("Port dropdown clicked")
except TimeoutException:
    logging.error("Port dropdown element not found")
    driver.quit()
    exit()

time.sleep(1)

# Type the port of embarkation
try:
    port_dropdown_element.send_keys(port_of_embarkation)
    logging.info(f"Typed port of embarkation: {port_of_embarkation}")
except Exception as e:
    logging.error(f"Failed to type port of embarkation: {e}")
    driver.quit()
    exit()

# Press Down followed by Enter to select the port
try:
    port_dropdown_element.send_keys(Keys.DOWN, Keys.RETURN)
    logging.info("Pressed Enter to select port")
except Exception as e:
    logging.error(f"Failed to press Enter: {e}")
    driver.quit()
    exit()

# Verify the port of embarkation was correctly entered (uses the verify_input_field helper)
try:
    if not verify_input_field(driver, "//*[@data-path='arrival.embarkPort']", port_of_embarkation):
        logging.error("Failed to verify port of embarkation")
        driver.quit()
        exit()
except Exception as e:
    logging.error(f"Error during port of embarkation verification: {e}")
    driver.quit()
    exit()


# [--INTENDED DATE OF ARRIVAL--]

try:
    expected_date_of_arrival = df.loc[0, 'Intended Date of Arrival']  # e.g., "February 2, 2025"
    logging.info(f"Intended Date of Arrival from sheet: {expected_date_of_arrival}")
    date_obj = datetime.strptime(expected_date_of_arrival, '%B %d, %Y')  # parse "February 2, 2025"
except KeyError:
    logging.error("Column 'Intended Date of Arrival' not found in DataFrame")
    logging.error(f"Available columns: {df.columns}")
    driver.quit()
    exit()
except ValueError as ve:
    logging.error(f"Failed to parse date '{expected_date_of_arrival}': {ve}")
    driver.quit()
    exit()

time.sleep(1)

# Click the date input field
try:
    date_input_element = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//*[@data-path='arrival.travelDate']"))
    )
    date_input_element.click()
    logging.info("Date input field clicked")
except TimeoutException:
    logging.error("Date input field not found")
    driver.quit()
    exit()

time.sleep(1)

# Navigate through the calendar and select the correct date
try:
    month_year = date_obj.strftime('%B %Y')
    day = str(date_obj.day)
    while True:
        current_month_year = wait.until(
            EC.visibility_of_element_located(
                (By.XPATH, "//button[@class='mantine-focus-auto m_f6645d97 mantine-DatePickerInput-calendarHeaderLevel m_87cf2631 mantine-UnstyledButton-root']")
            )
        ).text
        logging.info(f"Current month and year: {current_month_year}")
        if current_month_year == month_year:
            break
        next_btn = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//button[@data-direction='next']"))
        )
        next_btn.click()
        time.sleep(0.5)

    day_xpath = f"//button[@aria-label='{day} {month_year}']"
    logging.info(f"Day XPath: {day_xpath}")
    day_button = wait.until(EC.element_to_be_clickable((By.XPATH, day_xpath)))
    day_button.click()
    logging.info(f"Selected date: {expected_date_of_arrival}")
except Exception as e:
    logging.error(f"Failed to select date: {e}")
    driver.quit()
    exit()

# Verify the intended date of arrival
try:
    time.sleep(1)  # allow UI to update
    found_date_text = date_input_element.text.strip()  # e.g., "February 2, 2025"
    logging.info(f"Verification: found date text '{found_date_text}', expected '{expected_date_of_arrival}'")

    # Parse each date and compare as date objects
    found_dt = datetime.strptime(found_date_text, '%B %d, %Y')
    if found_dt.date() != date_obj.date():
        logging.error("Failed to verify intended date of arrival")
        driver.quit()
        exit()
except Exception as e:
    logging.error(f"Error during intended date of arrival verification: {e}")
    driver.quit()
    exit()


# [--NEXT STEP (STAGE 2) BUTTON--]

# Click the 'Next step' button to move to stage 2 of the application

time.sleep(1)

def click_next_step_button():
    try:
        next_step_button = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//button[@type='submit' and contains(@class, 'bg-blue-500')]"))
        )
        next_step_button.click()
        logging.info("Clicked 'Next step' button")
    except TimeoutException:
        logging.error("Failed to find 'Next step' button")
        driver.quit()
        exit()
    except Exception as e:
        logging.error(f"Error clicking 'Next step' button: {e}")
        driver.quit()
        exit()

# Call the function to click the 'Next step' button
click_next_step_button()

# Verify the 'Next step' button was clicked (if applicable)
try:
    # Verify that the next section is loaded by checking for the presence of the "Personal Information" header
    next_section_element = wait.until(
        EC.presence_of_element_located((By.XPATH, "//h1[text()='Personal Information']"))
    )
    logging.info("Successfully moved to the next step")
except TimeoutException:
    logging.error("Failed to move to the next step")
    driver.quit()
    exit()
except Exception as e:
    logging.error(f"Error during verification of next step: {e}")
    driver.quit()
    exit()





# ----------------------------------------------------------------STAGE 2: PERSONAL----------------------------------------------------------------






























    













# Keep the browser open for 5 seconds after all actions
time.sleep(5)

driver.quit()

