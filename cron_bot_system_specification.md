# Technical Specification: Cron-Job Based Bot Processing System

## Section 1: Bot Script Details & Invocation

**1.1. Overview**

The system will utilize individual, location-specific Python scripts (referred to as "bots") to automate the process of filling out online forms associated with customer orders. These bots are triggered by a central cron job script.

**1.2. Bot Script Characteristics**

*   **Nature**: Each bot will be a standalone command-line Python script (e.g., `Barbados_form_1.py`).
*   **Location**: Bot scripts will be stored within the project under a structured directory, preliminarily `external/bots/<LocationName>_form/<script_name>.py`. The exact naming convention and structure should be finalized by the developer for scalability.
*   **Input**: Each bot script will require a single command-line argument: the `order_id` (UUID) of the order it needs to process.
*   **Data Retrieval**: Upon receiving an `order_id`, the bot script is responsible for connecting to the Django application's database and fetching all necessary information to complete its task. This includes:
    *   Customer details from the `order` model.
    *   Location-specific form data from the relevant model (e.g., `BarbadosForm` for a Barbados order).
    *   Any other related data required for the form submission.
*   **Core Task**: The primary function of each bot is to use the retrieved data to fill out and submit the designated online form for the customer.
*   **Idempotency**: While not strictly required for the initial version, bot scripts should ideally be designed with idempotency in mind for future enhancements (i.e., running the bot multiple times for the same order, if it previously failed mid-way, should not cause duplicate submissions or adverse effects if the form was already partially or fully submitted).

**1.3. Main Cron Job Script (`process_orders_for_bots.py`)**

A Django management command, named `process_orders_for_bots.py`, will serve as the main script executed by the system's cron scheduler.

*   **Execution**: This script will be run at a configurable interval (e.g., every 5 minutes).
*   **Responsibilities**:
    1.  **Query Eligible Orders**: The script will query the database for all `order` records with a `status` of `'criminal_check_passed'`.
    2.  **Order Iteration**: For each eligible order found:
        *   **Status Update (Pre-processing)**: Immediately update the `order.status` to `'bot_processing'` and save the change. This acts as a lock to prevent the same order from being picked up by concurrent or subsequent cron runs if the bot script takes time to execute.
        *   **Bot Script Identification**: Determine the correct location-specific bot script to execute based on the `order.location.location_name`. A robust mapping mechanism (e.g., configuration file, database table, or strict naming convention) will be required to associate locations with their respective bot scripts.
        *   **Bot Script Invocation**: Execute the identified bot script as a separate subprocess (e.g., using Python's `subprocess` module). The `order_id` will be passed as a command-line argument to the bot script.
        *   **Timeout**: The main script should implement a timeout (e.g., 5-10 minutes) for each bot subprocess. If a bot exceeds this timeout, it should be considered a failure.
    3.  **Logging**: The script will maintain detailed logs of its operations, including orders processed, bots invoked, and any errors encountered during its own execution or reported by bot scripts (via exit codes or captured output).

**1.4. Bot Script Outcome Communication**

A dual mechanism will be used for bots to communicate their success or failure:

1.  **Direct Database Updates (Primary)**:
    *   **On Success**: The bot script, after successfully completing the form submission, will directly update the corresponding `order.status` in the database to `'bot_completed_form'`.
    *   **On Failure**: If the bot script fails to complete the form submission for any reason, it will:
        1.  Update the `order.status` to `'bot_submission_failed'`.
        2.  Create a new record in the `FailedSubmission` model, linking to the `order` and including a descriptive `error_message` detailing the cause of failure.

2.  **Exit Codes (Secondary/Confirmatory)**:
    Bot scripts will use specific exit codes to signal their outcome to the invoking `process_orders_for_bots.py` script. This provides a fallback/confirmatory mechanism.
    *   `1`: Success (form filled, `order.status` updated to `'bot_completed_form'`).
    *   `0`: General Failure (form not filled, `order.status` updated to `'bot_submission_failed'`, `FailedSubmission` record created).
    *   `2`: Failure due to External System Maintenance (form not filled, `order.status` updated to `'bot_submission_failed'`, `FailedSubmission` record created with a specific "system maintenance" message).
    *   *Additional codes can be defined for other specific, common failure scenarios if deemed useful for automated categorization or retry logic.*

    The `process_orders_for_bots.py` script will capture these exit codes and log them. If a bot script exits unexpectedly (e.g., crashes) or with an unhandled exit code *before* it could update the database, the main script should attempt to set the `order.status` to `'bot_submission_failed'` and create a generic `FailedSubmission` entry.

**1.5. Error Handling within Bot Scripts**

Bot scripts must include robust error handling to:
*   Catch exceptions during web interactions (e.g., network issues, unexpected page structures, elements not found).
*   Identify specific failure conditions (e.g., "login failed," "form validation error," "system under maintenance").
*   Ensure that, in case of any failure, the `order.status` is correctly updated to `'bot_submission_failed'` and a detailed `FailedSubmission` record is created.

---

## Section 2: Concurrency, Locking, and Crash Recovery

**2.1. Order Locking**

*   **Mechanism**: As defined in Section 1.3, the `order.status` will be set to `'bot_processing'` before a bot script is invoked for that order.
*   **Purpose**: This status acts as a lock, preventing other instances of the main cron job (or the same job in a subsequent quick run) from attempting to process the same order simultaneously.

**2.2. Concurrency Management**

*   **Control**: The `process_orders_for_bots.py` script will manage the number of concurrent bot subprocesses it launches.
*   **`MAX_CONCURRENT_BOTS` Setting**: A configurable setting, `MAX_CONCURRENT_BOTS` (e.g., defaulting to 2), will be defined in the Django settings or as a constant within the main script.
*   **Operation**: The main script will query for all eligible orders (`'criminal_check_passed'`). However, it will only launch new bot subprocesses if its current count of active bot subprocesses is below `MAX_CONCURRENT_BOTS`. It will need to track the active subprocesses it has spawned.
*   **Scalability**: This approach allows the system to be scaled by adjusting the `MAX_CONCURRENT_BOTS` value based on order volume and server capacity.

**2.3. Main Cron Script Crash Recovery**

*   **Scenario**: If the `process_orders_for_bots.py` script itself crashes or is terminated unexpectedly, orders that were marked `'bot_processing'` by that instance might remain in that state without an active bot working on them.
*   **Handling Stuck Orders**:
    1.  **Identification**: On startup (or periodically within its main loop), the `process_orders_for_bots.py` script should identify orders that have been in the `'bot_processing'` status for an excessive duration. This duration (`STUCK_ORDER_THRESHOLD`) should be configurable (e.g., `3 * configured_bot_timeout` or a fixed value like 1 hour).
    2.  **Resolution**: For each such "stuck" order identified:
        *   Log a warning indicating a potential previous script crash and that the order is being reset.
        *   Set the `order.status` back to `'criminal_check_passed'`.
        *   Reset the `order.bot_retry_attempts` field (see section 2.4) to `0`.
        *   This allows the order to be fairly re-queued and processed in the current or a subsequent run.
    *   **Logging**: Detailed logging of this recovery process is essential for monitoring system stability.

**2.4. Bot Script Crash Recovery and Retries**

*   **Detection by Main Script**: The `process_orders_for_bots.py` script is responsible for detecting bot script failures that aren't gracefully handled by the bot itself. This includes:
    *   The bot subprocess exiting with an error code not explicitly defined for success or known failures (see Section 1.4).
    *   The bot subprocess being terminated due to exceeding its configured execution timeout.
    *   The bot subprocess crashing without updating the `Order` status or creating a `FailedSubmission` record.
*   **Retry Mechanism**: To handle transient issues that might cause a bot to crash, a retry mechanism will be implemented:
    1.  **`Order.bot_retry_attempts` Field**:
        *   A new integer field, `bot_retry_attempts`, must be added to the `Order` model.
        *   This field will store the number of times a bot execution has been attempted for this order due to a crash or timeout.
        *   It should default to `0`.
    2.  **`MAX_BOT_RETRIES` Setting**:
        *   A configurable setting, `MAX_BOT_RETRIES` (e.g., 1 or 2), will be defined (e.g., in Django settings). This determines how many times the main script will re-invoke a bot for an order after a crash/timeout.
    3.  **Retry Logic (within `process_orders_for_bots.py`)**:
        *   When the main script invokes a bot for an order:
            *   If the bot script execution fails due to a crash or timeout:
                1.  The main script increments `order.bot_retry_attempts` and saves the `Order` object.
                2.  It logs the failure, noting the order ID and the current retry attempt number.
                3.  If `order.bot_retry_attempts` is less than or equal to `MAX_BOT_RETRIES`:
                    *   The `order.status` should remain `'bot_processing'` (or be reset to `'criminal_check_passed'` if the main loop logic re-queries). The order will be eligible for another bot invocation attempt in a subsequent pass of the main script's processing loop or in the next cron run.
                4.  If `order.bot_retry_attempts` exceeds `MAX_BOT_RETRIES`:
                    *   The main script will definitively mark the order as failed.
                    *   Set `order.status` to `'bot_submission_failed'`.
                    *   Create a `FailedSubmission` record. The `error_message` should indicate that the bot failed after reaching the maximum retry attempts due to repeated crashes or timeouts.
                    *   Log this final failure.
*   **Bot "Cleanup"**:
    *   The primary "cleanup" responsibility lies with the `process_orders_for_bots.py` script, which ensures the `Order` status and `FailedSubmission` records accurately reflect the outcome, especially after retries are exhausted.
    *   If a bot script crashes while interacting with an external website (e.g., after filling some fields but before submitting), there is generally no mechanism for transactional rollback of those external actions. The retry mechanism provides a chance for the bot to complete the entire process successfully on a subsequent attempt.
    *   Bot scripts themselves should still aim for graceful error handling (as per Section 1.5) to report specific issues. The main script's crash detection and retry logic serve as a safety net for unhandled exceptions or timeouts within the bots.

    ---

## Section 3: Bot Data Requirements

**3.1. Data Sourcing Principle**

*   Each location-specific bot script will receive an `order_id` as a command-line argument from the main cron script (`process_orders_for_bots.py`).
*   Using this `order_id`, the bot script is responsible for connecting to the Django application's database and fetching all necessary data to complete its form-filling task.

**3.2. Primary Data Models**

The data required by a bot will primarily be sourced from two models:

1.  **The `Order` Model (`orders/models.py`)**:
    *   This model will provide general order information and customer identification details (e.g., `first_name`, `surname`, `customer_email`).
2.  **Location-Specific Form Models (e.g., `BarbadosForm` in `orders/models.py`)**:
    *   For each location (e.g., Barbados), there will be a corresponding Django model (e.g., `BarbadosForm`) that holds all the specific data points required to fill out that location's online entry form.
    *   This includes, but is not limited to: personal details, passport/travel document information, travel itinerary details, accommodation details, health declarations, and any other questions posed by the external form.
    *   The bot script will query this location-specific model, filtered by the `order_id`, to retrieve all necessary answers.

**3.3. Developer Context**

*   The developer is expected to be familiar with the full schema of these location-specific form models and the complete set of data fields each bot will need to extract.
*   The bot scripts will need to be programmed to retrieve all relevant fields from these models to ensure successful form completion.

**3.4. Data Security**

*   Bot scripts will be handling Personally Identifiable Information (PII) and other sensitive data.
*   Access to the database from bot scripts must be secure.
*   Consideration should be given to minimizing the exposure of sensitive data (e.g., only fetching what is necessary, secure logging practices if any data is logged by the bot itself).

---

## Section 4: Enhancements to `FailedSubmission` Model

To improve the tracking, diagnosis, and management of failed bot submissions, the existing `FailedSubmission` model (in `orders/models.py`) will be enhanced.

**4.1. Purpose of `FailedSubmission` Records**

A `FailedSubmission` record is created when:
*   A bot script explicitly reports a failure (e.g., cannot log in, form validation error, external site maintenance).
*   The main cron script (`process_orders_for_bots.py`) detects a bot crash or timeout, and all automated retries (as defined by `Order.bot_retry_attempts` and `MAX_BOT_RETRIES`) have been exhausted.

**4.2. Proposed `FailedSubmission` Model Fields**

The model will include the following fields:

*   `id` (UUIDField, primary_key=True, default=uuid.uuid4, editable=False): Unique identifier for the failure record.
*   `order` (ForeignKey to `Order`, on_delete=models.CASCADE, related_name='failed_submissions'): Link to the specific order that failed.
*   `error_message` (TextField): A detailed message describing the error encountered. This can be populated by the bot script or the main cron script.
*   `failure_type` (CharField, max_length=50): A categorized type of failure to aid in analysis and filtering. Choices will include:
    *   `'BOT_CRASH'` (Bot script crashed unexpectedly)
    *   `'TIMEOUT'` (Bot script exceeded its execution timeout)
    *   `'SITE_UNDER_MAINTENANCE'` (Bot detected the external site was unavailable for maintenance)
    *   `'USER_APPLICATION_DATA_INCOMPLETE'` (Bot determined required data from the order/form model was missing or insufficient)
    *   `'FORM_VALIDATION_ERROR'` (External form rejected the submitted data)
    *   `'UNKNOWN'` (Default or undetermined failure cause)
*   `automated_retry_count_at_failure` (IntegerField): Stores the value of the `order.bot_retry_attempts` field at the time this `FailedSubmission` record was created. This indicates how many automated retries were performed by the main cron script before this final failure was logged.
*   `created_at` (DateTimeField, auto_now_add=True): Timestamp of when this failure record was initially created (renaming `last_attempt` or ensuring `auto_now_add` behavior).
*   `is_reviewed` (BooleanField, default=False): A flag set by an administrator (e.g., via Django admin) to indicate that this failure has been reviewed.
*   `admin_notes` (TextField, blank=True, null=True): A field for administrators to add notes or comments regarding their investigation or resolution steps.
*   `reviewed_by` (ForeignKey to `django.contrib.auth.models.User`, on_delete=models.SET_NULL, null=True, blank=True): A link to the administrator user who reviewed or took action on this failure.
*   `resolved_at` (DateTimeField, null=True, blank=True): A timestamp set by an administrator when they consider this specific failure instance to be administratively resolved (e.g., after data correction and re-queueing the order).

**4.3. Removal of Redundant Field**

*   The existing `retry_attempts` field in the `FailedSubmission` model will be removed, as its intended purpose is now better served by the `automated_retry_count_at_failure` field in conjunction with the `bot_retry_attempts` field on the `Order` model.

**4.4. Workflow Integration**

*   The Django admin interface for the `FailedSubmission` model should be enhanced to allow administrators to easily set `is_reviewed`, add `admin_notes`, link `reviewed_by`, and set `resolved_at`.
*   Filtering options in the admin based on `failure_type`, `is_reviewed`, and `resolved_at` will be beneficial.

---





