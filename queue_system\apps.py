from django.apps import AppConfig
from django.conf import settings
import threading
import time

class QueueSystemConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'queue_system'

    def ready(self):
        import queue_system.signals
        print("Queue system signals connected!")

        # # Auto-start workers in development mode
        # if settings.DEBUG and not self._is_management_command():
        #     print("🚀 Initializing auto-start location workers...")
        #     # Use the worker manager
        #     from queue_system.worker_manager import initialize_auto_workers
        #     initialize_auto_workers()

    def _is_management_command(self):
        """Check if we're running a management command (avoid starting workers for migrations, etc.)"""
        import sys
        return len(sys.argv) > 1 and sys.argv[1] in [
            'migrate', 'makemigrations', 'collectstatic', 'test',
            'shell', 'dbshell', 'createsuperuser', 'loaddata', 'dumpdata'
        ]
