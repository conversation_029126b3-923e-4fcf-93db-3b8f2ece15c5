from .models import order as Order, CriminalCheckDatabase, CriminalCheck, FailedSubmission, BarbadosForm #noqa
from django.db.models.signals import post_save
from django.dispatch import receiver
from external.api_clients.opensanctions import OpenSanctionsClient
from django.conf import settings
import logging

logger = logging.getLogger(__name__)



"""
When a new order is created, automatically create criminal check entries
for all active criminal check databases.
"""

# Signal handler: Automatically creates criminal checks when a new order is created
@receiver(post_save, sender=Order)
def create_criminal_checks(sender, instance, created, **kwargs):

    # Only run this code for newly created orders, not updates
    if created:
        # Get all active criminal check databases from the system
        active_databases = CriminalCheckDatabase.objects.filter(is_active=True)
        
        # Create a separate criminal check for each database
        for database in active_databases:
            # Link each check to both this order and the specific database
            CriminalCheck.objects.create(
                order=instance,
                database=database
            )

        process_new_order_checks(instance)


"""
When a criminal check is updated, update the parent order's status
based on the check result.
"""

# Signal handler: Updates order status and potentially triggers the Barbados bot when a criminal check is completed
@receiver(post_save, sender=CriminalCheck)
def update_order_status_after_criminalcheck_completed(sender, instance, created, **kwargs):
    # Skip if this is a new check being created
    if created:
        return
    
    # Only process if the check status is not pending
    if instance.check_status != 'pending':
        
        # Get the associated order
        order = instance.order
            
        # Update the status of the order object based on the check result
        if instance.check_status == 'passed':
            order.status = 'criminal_check_passed'
            # Just update the status - queue_system signals will handle the rest
            order.save(update_fields=['status'])
            
        elif instance.check_status == 'failed':
            order.status = 'criminal_check_failed'
            order.save(update_fields=['status'])



def process_new_order_checks(order):
    # Get all pending checks for this order
    pending_checks = CriminalCheck.objects.filter(
        order=order,
        check_status='pending'
    )
    
    if not pending_checks.exists():
        return
    
    # Create API client
    client = OpenSanctionsClient(settings.OPENSANCTIONS_API_KEY)
    
    # Process each check
    for check in pending_checks:
        try:
            # Get the person's name and run the check
            name = f"{check.order.first_name} {check.order.surname}"
            passed = client.check_person(name)
            
            # Update the check status
            check.check_status = 'passed' if passed else 'failed'
            check.save()
            
            logger.info(f"Auto-processed check for {name}: {'PASSED' if passed else 'FAILED'}")
            
        except Exception as e:
            logger.error(f"Error auto-processing check: {str(e)}")
            # Don't update status if there's an error
            continue        




def initiate_barbados_bot(order_instance):
    """
    Initiate the Barbados bot for an order.
    This function is used both directly and as a fallback when the queue system is unavailable.
    """
    # Use the procedural function instead of a class
    if BarbadosForm.objects.filter(order=order_instance).exists():
        try:
            form = BarbadosForm.objects.get(order=order_instance)
            # Try the new path first
            try:
                from external.bots.Barbados_form.Barbados_form_1 import run_barbados_bot
            except ImportError:
                # Fall back to the old path if the new one doesn't exist
                from external.bots.Barbados_form.Barbados_form_1 import run_barbados_bot
                
            logger.info(f"Bot initialized for {order_instance.first_name} {order_instance.surname} {order_instance.id}")
            
            # Run the bot with the form data
            try:
                result = run_barbados_bot(form)
            except Exception as e:
                logger.error(f"Error launching Barbados bot: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())

            
            if result:
                logger.info(f"Successfully completed form for {order_instance.first_name} {order_instance.surname}")
                # Update order status
                order_instance.status = 'bot_completed_form'
                order_instance.save(update_fields=['status'])
            else:
                logger.error(f"Failed to complete form for {order_instance.first_name} {order_instance.surname}")
                # Update order status
                order_instance.status = 'bot_submission_failed'
                order_instance.save(update_fields=['status'])
                              
        except Exception as e:
            logger.error(f"Error launching Barbados bot: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    else:
        logger.info(f"No Barbados form found for {order_instance.first_name} {order_instance.surname} {order_instance.id}")



# Comment out or remove this signal handler as it's now handled by queue_system.signals
# @receiver(post_save, sender=Order)
# def trigger_bot_on_order_status_change(sender, instance, created, **kwargs):
#     """
#     Trigger the Barbados bot when an order's status changes to 'criminal_check_passed'.
#     This centralizes the bot triggering logic to respond to the order status
#     regardless of what caused the status change.
#     """
#     # Skip newly created orders (they'll start as 'pending')
#     if created:
#         return
#         
#     # Only trigger the bot when status is 'criminal_check_passed'
#     if instance.status == 'criminal_check_passed':
#         logger.info(f"Order status changed to 'criminal_check_passed' for {instance.first_name} {instance.surname}. Triggering Barbados bot.")
#         initiate_barbados_bot(instance)
