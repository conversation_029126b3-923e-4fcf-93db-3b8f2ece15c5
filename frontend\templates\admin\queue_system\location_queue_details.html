{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .location-dashboard {
        padding: 20px;
    }
    .location-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding: 20px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
    }
    .worker-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .worker-control-btn {
        padding: 8px 12px;
        border: 1px solid #007cba;
        background: white;
        color: #007cba;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }
    .worker-control-btn:hover {
        background: #007cba;
        color: white;
    }
    .worker-control-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .queue-section {
        margin-bottom: 30px;
    }
    .queue-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .job-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .job-table th,
    .job-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }
    .job-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }
    .job-table tr:hover {
        background: #f8f9fa;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }
    .status-queued { background: #fff3cd; color: #856404; }
    .status-processing { background: #d1ecf1; color: #0c5460; }
    .status-requeued { background: #e2e3e5; color: #383d41; }
    .priority-badge {
        background: #f8d7da;
        color: #721c24;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
    }
    .age-indicator {
        font-size: 12px;
        color: #666;
    }
    .age-old { color: #dc3545; font-weight: bold; }
    .btn-link {
        color: #007cba;
        text-decoration: none;
        font-size: 12px;
    }
    .btn-link:hover {
        text-decoration: underline;
    }
    .max-workers-input {
        width: 60px;
        padding: 4px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="location-dashboard">
    <!-- Location Header with Worker Controls -->
    <div class="location-header">
        <div>
            <h1>📍 {{ location.location_name }}</h1>
            <p style="margin: 5px 0; color: #666;">Queue Details and Worker Management</p>
        </div>
        
        <div class="worker-controls">
            <span style="margin-right: 15px;">
                👥 Workers: <strong>{{ config.active_workers }}/{{ config.max_workers }}</strong>
            </span>
            
            <button class="worker-control-btn" onclick="adjustWorkers('decrease')" 
                    {% if config.active_workers <= 0 %}disabled{% endif %}>
                ➖ Decrease
            </button>
            
            <button class="worker-control-btn" onclick="adjustWorkers('increase')"
                    {% if config.active_workers >= config.max_workers %}disabled{% endif %}>
                ➕ Increase
            </button>
            
            <input type="number" class="max-workers-input" id="maxWorkersInput" 
                   value="{{ config.max_workers }}" min="1" max="10">
            
            <button class="worker-control-btn" onclick="setMaxWorkers()">
                Set Max
            </button>
        </div>
    </div>
    
    <!-- Active Queue Section -->
    <div class="queue-section">
        <div class="queue-header">
            <h2>🔄 Active Queue ({{ active_jobs.count }} jobs)</h2>
            <small style="color: #666;">Jobs currently queued, processing, or requeued</small>
        </div>
        
        {% if active_jobs %}
        <table class="job-table">
            <thead>
                <tr>
                    <th>Job ID</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Age</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in active_jobs %}
                <tr>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">
                            #{{ job.id }}
                        </a>
                    </td>
                    <td>{{ job.order.first_name }} {{ job.order.surname }}</td>
                    <td>
                        <span class="status-badge status-{{ job.status }}">
                            {% if job.status == 'queued' %}⏳{% elif job.status == 'processing' %}🔄{% elif job.status == 'requeued' %}🔄{% endif %}
                            {{ job.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {% if job.priority_flag %}
                            <span class="priority-badge">⚡ HIGH</span>
                        {% else %}
                            Normal
                        {% endif %}
                    </td>
                    <td>
                        <span class="age-indicator {% if job.age_hours > 24 %}age-old{% endif %}">
                            {% if job.age_hours < 1 %}
                                {{ job.age_hours|floatformat:0 }}m
                            {% else %}
                                {{ job.age_hours|floatformat:1 }}h
                            {% endif %}
                        </span>
                    </td>
                    <td>{{ job.created_at|date:"M d, H:i" }}</td>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">View</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666; background: white; border-radius: 8px;">
            ✅ No jobs in active queue
        </div>
        {% endif %}
    </div>
    
    <!-- Waiting Queue Section -->
    <div class="queue-section">
        <div class="queue-header">
            <h2>⏰ Waiting Queue ({{ waiting_jobs.count }} jobs)</h2>
            <small style="color: #666;">Jobs scheduled for future processing</small>
        </div>
        
        {% if waiting_jobs %}
        <table class="job-table">
            <thead>
                <tr>
                    <th>Job ID</th>
                    <th>Customer</th>
                    <th>Scheduled For</th>
                    <th>Priority</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in waiting_jobs %}
                <tr>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">
                            #{{ job.id }}
                        </a>
                    </td>
                    <td>{{ job.order.first_name }} {{ job.order.surname }}</td>
                    <td>{{ job.scheduled_for|date:"M d, Y H:i" }}</td>
                    <td>
                        {% if job.priority_flag %}
                            <span class="priority-badge">⚡ HIGH</span>
                        {% else %}
                            Normal
                        {% endif %}
                    </td>
                    <td>{{ job.created_at|date:"M d, H:i" }}</td>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">View</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666; background: white; border-radius: 8px;">
            📅 No jobs scheduled for later
        </div>
        {% endif %}
    </div>
    
    <!-- Navigation -->
    <div style="text-align: center; margin-top: 30px;">
        <a href="{% url 'queue_system:queue_overview' %}" class="btn-link">← Back to Overview</a>
        <span style="margin: 0 20px;">|</span>
        <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" class="btn-link">All Jobs</a>
    </div>
</div>

<script>
function adjustWorkers(action) {
    fetch('{% url "queue_system:adjust_workers" location.id %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: 'action=' + action
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

function setMaxWorkers() {
    const maxWorkers = document.getElementById('maxWorkersInput').value;
    fetch('{% url "queue_system:adjust_workers" location.id %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: 'action=set_max&max_workers=' + maxWorkers
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

// Auto-refresh every 15 seconds
setTimeout(function() {
    location.reload();
}, 15000);
</script>
{% endblock %}
