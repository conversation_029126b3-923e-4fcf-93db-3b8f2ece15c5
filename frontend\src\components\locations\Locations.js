import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import SearchComponent from '../shared/Search_component';
import {
    Box, Typography, Table, TableBody, TableCell,
    TableContainer, TableHead, TableRow, Paper,
    CircularProgress, Alert, Container
    // Button, // No longer directly needed from MUI here
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton'; // IconButton is different from Button
import CustomButton from '../shared/Custom_button'; // Import CustomButton
import { useApiService } from '../../hooks/useApiService';

function LocationsTable() {
    const [locations, setLocations] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [userMessage, setUserMessage] = useState(null);
    const navigate = useNavigate();

    const {
        data: apiResponseData,
        loading,
        error: apiCallError,
        request,
        setError: setApiError
    } = useApiService();

    useEffect(() => {
        request('/api/locations/');
    }, [request]);

    useEffect(() => {
        if (apiResponseData && apiResponseData.results) {
            setLocations(apiResponseData.results);
            setUserMessage(null);
        } else if (apiResponseData && Array.isArray(apiResponseData) && !apiResponseData.deleted) {
            setLocations(apiResponseData);
            setUserMessage(null);
        }
    }, [apiResponseData]);


    const handleLocationSearch = (term) => {
        setSearchTerm(term);
    };

    const handleDelete = async (id) => {
        if (!window.confirm("Are you sure you want to delete this location?")) return;

        setApiError(null);
        setUserMessage(null);

        const { success } = await request(`/api/locations/${id}/`, { method: 'DELETE' });

        if (success) {
            setUserMessage('Location deleted successfully!');
            setLocations(prevLocations => prevLocations.filter(loc => loc.id !== id));
        }
    };

    const filteredLocations = locations.filter(locationItem =>
        locationItem.location_name &&
        locationItem.location_name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <Container maxWidth={false} sx={{ mt: 4, mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h4" component="h1">
                    Manage Locations
                </Typography>
            </Box>

            {userMessage && (
                <Alert
                    severity="success"
                    onClose={() => setUserMessage(null)}
                    sx={{ mb: 2 }}
                >
                    {userMessage}
                </Alert>
            )}

            {apiCallError && (
                <Alert
                    severity="error"
                    onClose={() => setApiError(null)}
                    sx={{ mb: 2 }}
                >
                    {apiCallError}
                </Alert>
            )}

            <SearchComponent onSearchSubmit={handleLocationSearch} placeholder="Search locations..." sx={{ mb: 2 }} />

            {loading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                    <CircularProgress />
                </Box>
            )}

            {!loading && apiResponseData && (
                <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
                    <Table sx={{ minWidth: 650 }} aria-label="locations table">
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{ width: '30%', whiteSpace: 'nowrap' }}>ID</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Location Name</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Traveller Price</TableCell>
                                <TableCell sx={{ whiteSpace: 'nowrap' }}>Cost Price</TableCell>
                                <TableCell align="right"
                                    sx={{
                                        justifyContent: 'flex-end',
                                        display: 'flex',
                                        whiteSpace: 'nowrap',
                                        paddingLeft: '8px',
                                    }}
                                >
                                    Delete
                                </TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {filteredLocations.length === 0 && !apiCallError ? (
                                <TableRow>
                                    <TableCell colSpan={5} align="center">
                                        No locations found matching your search.
                                    </TableCell>
                                </TableRow>
                            ) : (
                                filteredLocations.map((locationItem) => (
                                    <TableRow
                                        key={locationItem.id}
                                        component={Link} // Add this to make the row a link
                                        to={`/locations/${locationItem.id}/edit`} // Add the navigation path
                                        sx={{ // Add styling for link and hover effect
                                            '&:last-child td, &:last-child th': { border: 0 },
                                            textDecoration: 'none',
                                            color: 'inherit',
                                            cursor: 'pointer',
                                            '&:hover': { backgroundColor: '#f5f5f5' }
                                        }}
                                    >
                                        <TableCell component="th" scope="row">{locationItem.id}</TableCell>
                                        <TableCell>
                                            {/* The Link component here is now redundant as the whole row is a link.
                                                You can simplify this to just display the location name,
                                                or keep it if you want specific styling for the name itself.
                                                For simplicity and to avoid nested links, let's simplify. */}
                                            {locationItem.location_name}
                                        </TableCell>
                                        <TableCell>${parseFloat(locationItem.traveller_price).toFixed(2)}</TableCell>
                                        <TableCell>${parseFloat(locationItem.cost_price).toFixed(2)}</TableCell>
                                        <TableCell
                                            align="right"
                                            sx={{
                                                display: 'flex',
                                                justifyContent: 'flex-end',
                                                paddingLeft: '8px',
                                            }}
                                        >
                                            <IconButton
                                                color="error"
                                                onClick={(e) => {
                                                    e.preventDefault(); // Prevent row navigation when clicking delete
                                                    e.stopPropagation(); // Stop event bubbling
                                                    handleDelete(locationItem.id);
                                                }}
                                                size="small"
                                            >
                                                <DeleteIcon fontSize="small" />
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <CustomButton
                    component={Link}
                    to="/locations/add"
                    variant="contained"
                    color="primary"
                    sx={{ p: 1.5 }} // Ensuring consistent padding
                >
                    Add Location
                </CustomButton>
            </Box>
        </Container>
    );
}

export default LocationsTable;