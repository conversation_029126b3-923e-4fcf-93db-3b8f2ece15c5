# Generated by Django 5.1.6 on 2025-03-06 12:57

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("orders", "0007_barbadosform_accommodation_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="barbadosform",
            name="accommodation_address",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="barbadosform",
            name="accommodation_name",
            field=models.CharField(
                blank=True,
                choices=[
                    ("67_palm_crest", "67 Palm Crest Apartments"),
                    ("adelcrombie", "Adelcrombie Beach House"),
                    ("adulo", "Adulo Apartments"),
                    ("annjenn", "Annjenn Apartments"),
                    ("ascot_house", "Ascot House"),
                    ("beach_side_10", "Beach Side Apartments 10"),
                    ("best_e_prospect", "Best E Villas - Prospect"),
                    ("best_e_providence", "Best E. Villas - Providence"),
                    ("blue_ocean", "Blue Ocean Cottage"),
                    ("bournes", "Bourne's Gentle Breeze Apartments"),
                    ("carib_blue", "Carib Blue Apartments"),
                    ("chateau_blanc", "Chateau Blanc Apts On Sea"),
                    ("cherry_garden", "Cherry Garden Villa"),
                    ("cherry_tree", "Cherry Tree Apartments Limited"),
                    ("clearwater", "Clearwater Apartments"),
                    ("cotton_beach", "Cotton Beach Apartments"),
                    ("cumbers", "Cumber's Tropical Apartments"),
                    ("dover_woods", "Dover Woods Apartments"),
                    ("frontline", "Frontline Apartments"),
                    ("gibbs_glade", "Gibbs Glade Cottage & Garden Studios"),
                    ("halcyon_palm", "Halcyon Palm Apartments"),
                    ("healthy_horizons", "Healthy Horizons Apartment"),
                    ("inchcape", "Inchcape Seaside Villas"),
                    ("khrysann", "Khrysann - Kingsland"),
                    ("kings_beach", "Kings Beach Village"),
                    ("lantana", "Lantana Resort (The)"),
                    ("legend_gardens", "Legend Gardens Condos (The)"),
                    ("lighthouse", "Lighthouse Look Apartments"),
                    ("magic_isle", "Magic Isle Beach Apartments"),
                    ("maresol", "Maresol Beach Condominiums"),
                    ("marlane", "Marlane Apartments"),
                    ("maxwell", "Maxwell Park Apartments"),
                    ("melrose", "Melrose Beach Apartments"),
                    ("meridian", "Meridian Inn"),
                    ("miami_beach", "Miami Beach Apartments"),
                    ("mirabelle", "Mirabelle Apartments"),
                    ("monteray", "Monteray Apartments"),
                    ("moonraker", "Moonraker Hotel"),
                    ("mullins", "Mullins Grove Apartment Hotel"),
                    ("naniki", "Naniki Barbados (formerly Lush Life Nature Resort)"),
                    ("nautilus", "Nautilus Beach Apartments"),
                    ("ocean_bliss", "Ocean Bliss Apartments"),
                    ("ocean_sky", "Ocean Sky Apartments"),
                    ("ocean_spray", "Ocean Spray Apartments"),
                    ("pantherra", "Pantherra Terra"),
                    ("paradise", "Paradise Villas"),
                    ("pirates", "Pirates Inn"),
                    ("plum_tree", "Plum Tree Club on Rockley Golf Course"),
                    ("port_st_charles", "Port St. Charles"),
                    ("regent", "Regent Apartments"),
                    ("roman_beach", "Roman Beach Apts."),
                    ("rosebank", "Rosebank Apartments"),
                    ("sandy_bliss", "Sandy Bliss Apts"),
                    ("santa_neta", "Santa Neta Apartments"),
                    ("santosha", "Santosha Barbados"),
                    ("sea_foam", "Sea Foam Haciendas Apartments"),
                    ("shades", "Shades Apartment"),
                    ("southern_surf", "Southern Surf Beach Apartments"),
                    ("sun_n_sea", "Sun N' Sea"),
                    ("sweet_jewel", "Sweet Jewel Apartments"),
                    ("terraces", "The Terraces Suites"),
                    ("the_view", "The View"),
                    (
                        "villa_ilfracombe",
                        "Villa Ilfracombe (Bed & Breakfast Barbados Ltd)",
                    ),
                    ("villa_mia", "Villa Mia"),
                    ("villa_soilel", "Villa Soilel"),
                    ("west_rock", "West Rock Villas"),
                    ("white_sands", "White Sands Beach Condos"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="barbadosform",
            name="accommodation_type",
            field=models.CharField(
                choices=[
                    ("bed_breakfast", "Bed and Breakfast"),
                    ("apartment", "Apartment"),
                    ("guest_house", "Guest House"),
                    ("hotel", "Hotel"),
                    ("private_home", "Private Home"),
                    ("villa", "Villa"),
                    ("not_required", "Not Required (Comes in and Leaves Same Day)"),
                    ("other", "Other"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="barbadosform",
            name="permament_address",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="barbadosform",
            name="specify_accommodation",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
