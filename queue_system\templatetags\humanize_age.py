from django import template

register = template.Library()

@register.filter
def humanize_hours(hours):
    try:
        hours = float(hours)
    except (ValueError, TypeError):
        return "N/A"

    minutes = int(hours * 60)
    months, rem_minutes = divmod(minutes, 43800)  # 1 month = 30.42 days = 43800 minutes
    weeks, rem_minutes = divmod(rem_minutes, 10080)
    days, rem_minutes = divmod(rem_minutes, 1440)
    hrs, rem_minutes = divmod(rem_minutes, 60)

    parts = []
    if months:
        parts.append(f"{months}M")
    if weeks:
        parts.append(f"{weeks}W")
    if days:
        parts.append(f"{days}D")
    if hrs:
        parts.append(f"{hrs}h")

    return " ".join(parts) if parts else "0h"