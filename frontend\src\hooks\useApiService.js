import { useState, useCallback } from 'react';

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

async function parseError(response) {
    let errorDetail = `Request failed with status: ${response.status}`;
    try {
        const text = await response.text();
        try {
            const jsonData = JSON.parse(text);
            errorDetail = jsonData.detail || jsonData.message || JSON.stringify(jsonData, null, 2);
        } catch (e) {
            errorDetail = text || errorDetail;
        }
    } catch (e) {
        // Failed to even get text, stick with status
    }
    return errorDetail;
}

export function useApiService() {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const request = useCallback(async (url, options = {}) => {
        setLoading(true);
        setError(null);
        // Not resetting data here so previous data can be shown while new data loads if desired
        // setData(null); 

        const config = {
            credentials: 'include',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                ...options.headers,
            },
            ...options,
        };

        if (options.method && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method.toUpperCase())) {
            const csrfToken = getCookie('csrftoken');
            if (csrfToken) {
                config.headers['X-CSRFToken'] = csrfToken;
            } else {
                console.warn('CSRF token not found. Request might fail for mutating operations.');
            }
        }

        try {
            const response = await fetch(url, config);
            if (!response.ok) {
                const errorMsg = await parseError(response);
                throw new Error(errorMsg);
            }
            
            // For DELETE requests or other no-content success, indicate success differently
            if (response.status === 204 || options.method?.toUpperCase() === 'DELETE') {
                setData({ success: true, deleted: true }); // Indicate successful deletion/no content
                return { success: true, data: null };
            }
            const responseData = await response.json();
            setData(responseData);
            return { success: true, data: responseData };
        } catch (err) {
            setError(err.message);
            // setData(null); // Optionally clear data on error
            return { success: false, error: err.message };
        } finally {
            setLoading(false);
        }
    }, []);

    return { data, loading, error, request, setError };
}