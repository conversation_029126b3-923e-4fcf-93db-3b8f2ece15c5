from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from datetime import datetime, date, time # Import specific classes needed
from django.db.utils import OperationalError

import os
import time
import logging
import re
import django
import traceback
import sys

# Add the project to the system path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django Environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

# set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# Helper class for standardized element interactions.
class ElementHandler:

    def __init__(self, driver, wait, logger):   # ← add this
        self.driver = driver
        self.wait   = wait
        self.logger = logger    

    def click_element(self, xpath):
        try:
            # time.sleep(5)
            element = self.wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
            # self.driver.execute_script("arguments[0].click();", element)
            element.click()
            self.logger.info(f"clicked element with IDE: {xpath}")
            return element
        except Exception as e:
            self.logger.error(f"Error clicking element: {e}")
            return False

    # Handle drop down menus
    def dropdowns(self, element_xpath, option_selection_xpath):
        try:
            if self.click_element(element_xpath):
                self.logger.info(f"Selected click option {option_selection_xpath}")
                #time.sleep(5)
                if self.click_element(option_selection_xpath):
                    self.logger.info(f"Selected dropdown option {option_selection_xpath}")
                    return True
                else:
                    self.logger.info(f"Failed to select dropdown option: {option_selection_xpath}")
                    return False
            else:
                self.logger.info(f"Failed to open dropdown with element ID: {element_xpath}")
                return False
        except Exception as e:
            self.logger.error(f"Unable to handle dropdown {e}")
            return False
    

    # Handle text field
    def text_field(self, element_xpath, value):
        try:
            element = self.click_element(element_xpath)
            if element is False:
                return False
            element.clear()
            element.send_keys(value)
            self.logger.info(f"Typed the following text into the text field: {value}")
            return True
        except Exception as e:
            self.logger.error(f"Unable to handle text element with IDE: {element_xpath} {e}")
            return False



# MAIN CLASS BOT FOR FILLING OUT THE FORM------------------------------------------------------------
class BarbadosFormBot:

    # ---- Elemennt Locators ----
    # Residential status IDE (PERMANENT)
    RESIDENTIAL_STATUS_PERMANENT_XPATH = "//label[.//span[text()='Permanent Address Is In Barbados']]"
    # Residential status IDE (VISITOR)
    RESIDENTIAL_STATUS_VISITOR_XPATH = "//label[.//span[text()='Non-Resident/Visitor']]"
    # Entry method IDE (AIR)
    ENTRY_METHOD_AIR_XPATH = "//label[.//span[text()='Air']]"
    # Entry method IDE (SEA)
    ENTRY_METHOD_SEA_XPATH = "//label[.//span[text()='Sea']]"
    # Airline
    AIRLINE_DROPDOWN_XPATH = "//*[@data-path='arrival.carrierName']"
    # Flight number
    FLIGHT_NUMBER_XPATH = "//*[@data-path='arrival.carrierID']"
    # Vessel details
    VESSEL_NAME_XPATH = "//*[@data-path='arrival.carrierName']"
    # Vessel number
    VESSEL_NUMBER_XPATH = "//*[@data-path='arrival.carrierID']"
    # Country of embarkation
    COUNTRY_OF_EMBARKATION_XPATH = "//*[@data-path='arrival.embarkCountry']"
    # Port of embarkation
    PORT_OF_EMBARKATOIN_XPATH = "//*[@data-path='arrival.embarkPort']"
    # Intended arrival date
    INTENDED_ARRIVAL_DATE_XPATH = "//*[@data-path='arrival.travelDate']"
    # Intended arrival date - calendar header button
    INTENDED_ARRIVAL_HEADER_BTN_XPATH = "//button[contains(@class, 'mantine-DatePickerInput-calendarHeaderLevel')]"
    # Intended arrival date - Next month button
    INTENDED_ARRIVAL_NEXT_BTN_XPATH = "//button[@data-direction='next' and not(@data-disabled)]"
    # Intended arrival date - Day button xpath template
    INTENDED_ARRIVAL_DAY_BTN_XPATH = "//button[contains(@class, 'mantine-DatePickerInput-day') and @aria-label='{}' and not(@data-disabled)]"
    
    # First name text field
    FIRST_NAME_XPATH = "//*[@data-path='personal.firstName']"
    # Surname text field
    SURNAME_XPATH = "//*[@data-path='personal.lastName']"
    # Gender dropdown element
    GENDER_DROPDOWN_XPATH = "//*[@data-path='personal.gender']"
    # Gender selection
    GENDER_SELECTION_XPATH = "//div[@role='option' and @value='{gender}']"
    # Country of birth element
    COUNTRY_OF_BIRTH_XPATH = "//*[@data-path='personal.countryOfBirth']"
    # Date of birth element
    DATE_OF_BIRTH_XPATH = "//*[@data-path='personal.dob']"
    # Nationality element
    NATIONALITY_XPATH = "//*[@data-path='personal.nationality']"
    # Country of residence
    COUNTRY_OF_RESIDENCE_XPATH = "//*[@data-path='personal.countryOfResidence']"
    # Zip code
    ZIP_CODE_XPATH = "//*[@data-path='personal.zipCode']"
    # Email address
    EMAIL_ADDRESS_XPATH = "//*[@data-path='personal.email']"
    # Confirm email address
    CONFIRM_EMAIL_ADDRESS_XPATH = "//*[@data-path='personal.confirmEmail']"
    # Phone number field
    PHONE_NUMBER_XPATH = "//input[@type='tel']"
    # Authorize button
    AUTHORIZE_CHECKBOX_XPATH = "//*[@data-path='personal.authorizeUse']"
    
    # Travel document type dropdow
    TRAVEL_DOCUMENT_TYPE_XPATH = "//*[@data-path='travel.documentType']"
    # Travel document number field
    TRAVEL_DOCUMENT_NUMBER_XPATH = "//*[@data-path='travel.documentNum']"
    # Expiry date element
    TRAVEL_DOCUMENT_EXPIRY_XPATH = "//*[@data-path='travel.expiryDt']"
    # Visited countries element
    VISITED_COUNTRIES_XPATH = "//div[contains(@class, 'mantine-MultiSelect-wrapper')]"
    #Purpose of visit dropdown
    PURPOSE_OF_VISIT_XPATH = "//*[@data-path='destination.purposeOfVisit']"
    # Accommodation Type dropdown
    ACCOMMODATION_TYPE_XPATH = "//*[@data-path='destination.accommodationType']"
    # Accommodation Name dropdown
    ACCOMMODATION_NAME_XPATH = "//*[@data-path='destination.accommodationName']"
    # Accommodation Address text field    
    ACCOMMODATION_ADDRESS_XPATH = "//*[@data-path='destination.destinationAddressline1']"
    # Specify Accommodation type text field
    SPECIFY_ACCOMMODATION_TYPE_XPATH = "//*[@data-path='destination.specifyAccommodation']"

    # Next stage button
    NEXT_STAGE_BUTTON = "//button[@type='button' and contains(@class, 'bg-blue-500')]"


    # Initialize the bot with form data.
    def __init__(self, form_data):
        self.form_data = form_data
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)
        self.element_handler = None
        # Error tracking
        self.last_error = None
        self.error_details = None
        self.successful_steps = []
        self.failed_step = None

    # --- ERROR TRACKING METHODS ---------------------------------------------------#

    def _record_success(self, step_description):
        """Record a successful step"""
        self.successful_steps.append(step_description)
        self.logger.info(f"SUCCESS: {step_description}")

    def _record_error(self, step_description, error_message, error_type="bot_execution_error"):
        """Record an error with detailed information"""
        self.failed_step = step_description
        self.last_error = error_message
        self.error_details = {
            'failed_step': step_description,
            'error_message': error_message,
            'error_type': error_type,
            'successful_steps': self.successful_steps.copy(),
            'timestamp': datetime.now().isoformat()
        }
        self.logger.error(f"FAILED: {step_description} - {error_message}")

    def get_error_summary(self):
        """Get a formatted error summary for display"""
        if not self.last_error:
            return None

        summary_lines = []

        # Add successful steps
        for step in self.successful_steps[-3:]:  # Show last 3 successful steps
            summary_lines.append(f"[SUCCESS] {step}")

        # Add failed step
        if self.failed_step:
            summary_lines.append(f"[FAILED] {self.failed_step}")

        summary_lines.append("[RETRY] Job automatically requeued for retry")

        return "\n".join(summary_lines)

    # --- INTERNAL HELPER METHODS ---------------------------------------------------#

    # Handle parsing and formatting a date object       
    """    
    def parse_and_format_date(self, date_str_yyyy_mm_dd):
        try:
            # parse the string from database
            date_obj = datetime.strptime(date_str_yyyy_mm_dd, "%Y-%m-%d")
            self.logger.info(f"Successfully parsed arrival date: {date_obj}")
            return date_obj
        except ValueError:
            self.logger.error(f"The date string extracted ('{date_str_yyyy_mm_dd}') from the database, doesn't match the required format YYYY-MM-DD")
            return None
        except Exception as e:
            self.logger.error(f"An unexpected error occurred trying to parse the arrival date ('{date_str_yyyy_mm_dd}') {e}")
            return None   
    """    
    
    # Intended date of arrival helper method
    def intended_arrival_date_picker(self, arrival_date_object):
        if not arrival_date_object:
            self.logger.info("No valid date object received by the database")
            return False

        # create the header date string from date object (date_obj)
        calendar_header_string = arrival_date_object.strftime("%B %Y")
        self.logger.debug(f"Formatted for header check: '{calendar_header_string}'")
        # create the aria label 
        aria_label_date_string = f"{arrival_date_object.day} {arrival_date_object.strftime('%B')} {arrival_date_object.year}"
        self.logger.debug(f"Formatted for Aria Label {aria_label_date_string}")
    
        self.logger.info("Clicking date picker to open date selection.")
        if not self.element_handler.click_element(self.INTENDED_ARRIVAL_DATE_XPATH):
            self.logger.error("Problem clicking calander to open")
            return False

        # time.sleep(2)

        ########################################################################################

        # WebDriverWait(self.driver, 10).until(
        #     EC.visibility_of_element_located((By.XPATH, "//div[contains(@class, 'mantine-DatePickerInput-calendar')]"))
        # )

        # find all elements within the calendar - remove after
        available_dates = self.driver.find_elements(By.XPATH, "//button[contains(@class, 'mantine-DatePickerInput-day') and not(@data-disabled)]")
        self.logger.info(f"Found {len(available_dates)}")
        
        # logging the dates available within calendar picker - remove after
        for date_btn in available_dates:
            self.logger.info(f"Available date: {date_btn.get_attribute('aria-label')}")

    
        # time.sleep(2)
        
        ########################################################################################

        

        header_element = self.wait.until(EC.visibility_of_element_located((By.XPATH, self.INTENDED_ARRIVAL_HEADER_BTN_XPATH)))
        current_display = header_element.text

        self.logger.info(f"Cheking calendar's header date matches {calendar_header_string}")
        
        is_available = any(arrival_date_object.strftime("%#d %B %Y").lower() in d.get_attribute('aria-label').lower() for d in available_dates)

        if not is_available:
            arrival_date_object = date.today()
            calendar_header_string = arrival_date_object.strftime("%B %Y")
            self.logger.debug(f"Formatted for header check: '{calendar_header_string}'")


        if current_display == calendar_header_string:
            print("entering desired if")
            day_xpath = f"//button[contains(@class, 'mantine-DatePickerInput-day') and text()='{arrival_date_object.day}' and not(@data-disabled)]"
            if self.element_handler.click_element(day_xpath):
                self.logger.info(f"Successfully clicked on {day_xpath}")
                return True
        
        if not current_display == calendar_header_string:
            self.logger.info("Dates do not match - will enter into loop until dates match.")
            attempts = 0
            
            while current_display != calendar_header_string and attempts < 12:
                calendar_next_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, self.INTENDED_ARRIVAL_NEXT_BTN_XPATH)))
                calendar_next_button.click()
                self.logger.info(f"Clicking next button until header date matches {calendar_header_string}")
                time.sleep(0.5)
                header_element = self.wait.until(EC.visibility_of_element_located((By.XPATH, self.INTENDED_ARRIVAL_HEADER_BTN_XPATH)))
                current_display = header_element.text
                attempts += 1

            if attempts >= 12:
                self.logger.info("Could not find the correct target month/year after 12 attempts")
                return False
            
            self.logger.info("Found the correct month/year")

            day_xpath = f"//button[contains(@class, 'mantine-DatePickerInput-day') and text()='{arrival_date_object.day}' and not(@data-disabled)]"
            self.logger.info(f"Attempting to click day: {day_xpath}")

            if self.element_handler.click_element(day_xpath):
                self.logger.info(f"Successfully clicked on {day_xpath}")
                return True
            else:
                self.logger.error(f"Failed to click day button for {aria_label_date_string}")
                return False

    # --- END INTERNAL HELPER METHODS -----------------------------------------------#


    # Initialize the web driver and navigate to the website.
    def initialize(self):
        try:
            # initiate the webdriver.
            self.driver = webdriver.Chrome()
            self.wait = WebDriverWait(self.driver, 20)

            # initialize the ElementHanlder
            self.element_handler = ElementHandler(self.driver, self.wait, self.logger)

            # navigate to the website.
            self.driver.get("https://www.travelform.gov.bb/create")
            self.logger.info("Navigated to the Barbados Immigration Form Website.")
            return True

        except Exception as e:
            self.logger.error("An error occurred while initializing the bot.")
            self.logger.error(traceback.format_exc())
            self.cleanup()
            return False


    # Clean up when done or when an error occurs.
    def cleanup(self):
        if self.driver:
            self.driver.quit()
            self.logger.info("Webdriver closed.")


    # Main method to run the form filling process.
    def run(self):
        try:
            if not self.initialize():
                self._record_error("Bot initialization", "Failed to initialize the bot (WebDriver setup failed)", "webdriver_error")
                return False

            self._record_success("Bot successfully initialized and navigated to form")

            ################################
            ####### STAGE 1: ARRIVAL #######
            ################################
            self.logger.info("Starting Stage 1: Arrival")

            # HANDLE RESIDENTIAL STATUS
            residential_status = self.form_data.residential_status
            self.logger.info(f"Extracted 'Residential Status' from the database: {residential_status}")

            if residential_status == "Permanent Address Is In Barbados":
                if not self.element_handler.click_element(self.RESIDENTIAL_STATUS_PERMANENT_XPATH):
                    self._record_error("Residential status selection", f"Failed to click residential status: {residential_status}", "element_interaction_error")
                    return False
                self._record_success(f"Selected residential status: {residential_status}")
            elif residential_status == "Non-Resident/Visitor":
                if not self.element_handler.click_element(self.RESIDENTIAL_STATUS_VISITOR_XPATH):
                    self._record_error("Residential status selection", f"Failed to click residential status: {residential_status}", "element_interaction_error")
                    return False
                self._record_success(f"Selected residential status: {residential_status}")
            else:
                self._record_error("Residential status validation", f"Invalid residential status in database: {residential_status}", "invalid_order_data")
                return False

            # HANDLE ENTRY METHOD
            entry_method = self.form_data.how_are_you_entering
            self.logger.info(f"Extracted the 'Entry Method' from the database: {entry_method}")

            if entry_method == 'Air':
                if not self.element_handler.click_element(self.ENTRY_METHOD_AIR_XPATH):
                    self._record_error("Entry method selection", f"Failed to click entry method: {entry_method}", "element_interaction_error")
                    return False
                self._record_success(f"Selected entry method: {entry_method}")
            elif entry_method == 'Sea':
                if not self.element_handler.click_element(self.ENTRY_METHOD_SEA_XPATH):
                    self._record_error("Entry method selection", f"Failed to click entry method: {entry_method}", "element_interaction_error")
                    return False
                self._record_success(f"Selected entry method: {entry_method}")
            else:
                self._record_error("Entry method validation", f"Invalid entry method in database: {entry_method}", "invalid_order_data")
                return False
        
            # HANDLE AIRLINE QUESTIONS (IF ENTRY METHOD == 'AIR')
            if entry_method == 'Air':

                airline = self.form_data.airline
                self.logger.info(f"Extracted the Airline from the database: {airline}")

                if not self.element_handler.dropdowns(self.AIRLINE_DROPDOWN_XPATH, f"//div[@role='option']/span[text()='{airline}']/parent::div"):
                    self._record_error("Airline dropdown selection", f"Unable to select airline '{airline}' from dropdown", "element_interaction_error")
                    return False

                self._record_success(f"Selected airline: {airline}")

                # HANDLE FLIGHT NUMBER
                flight_number = self.form_data.flight_number
                self.logger.info(f"Extracted Flight number from the database: {flight_number}")

                if not self.element_handler.text_field(self.FLIGHT_NUMBER_XPATH, flight_number):
                    self._record_error("Flight number entry", f"Unable to enter flight number '{flight_number}' into field", "element_interaction_error")
                    return False

                self._record_success(f"Entered flight number: {flight_number}")
            
            # HANDLE VESSEL QUESTIONS (IF ENTRY METHOD == 'SEA')
            if entry_method == 'Sea':

                vessel_name = self.form_data.vessel_name
                self.logger.info(f"Extracted the Vessel name from the database: {vessel_name}")

                if not self.element_handler.text_field(self.VESSEL_NAME_XPATH, vessel_name):
                    self.logger.info(f"Unable to enter the vessel name ('{vessel_name}') into the vessel name field")
                    return False
                
                # HANDLE VESSEL REGISTRATION
                vessel_number = self.form_data.vessel_number
                self.logger.info(f"Extracted 'Vessel number' from the database: {vessel_number}")

                if not self.element_handler.text_field(self.VESSEL_NUMBER_XPATH, vessel_number):
                    self.logger.info(f"Unable to enter the vessel number ('{vessel_number}') into the vessel number field")
                    return False

            # HANDLE COUNTRY OF EMBARKATION
            country_of_embarkation = self.form_data.departure_country
            self.logger.info(f"Extracted the 'Country of embarkation' from the database: {country_of_embarkation}")
            if not self.element_handler.dropdowns(self.COUNTRY_OF_EMBARKATION_XPATH, f"//div[@role='option'][@value='{country_of_embarkation}']"):
                self._record_error("Country of embarkation selection", f"Unable to select country of embarkation '{country_of_embarkation}' from dropdown", "element_interaction_error")
                return False

            self._record_success(f"Selected country of embarkation: {country_of_embarkation}")

            # HANDLE PORT OF EMBARKATION
            port_of_embarkation = self.form_data.departure_airport
            self.logger.info(f"Extracted users 'Port of Embarkation' from the database: {port_of_embarkation}")
            if not self.element_handler.dropdowns(self.PORT_OF_EMBARKATOIN_XPATH, f"//div[@role='option'][@value='{port_of_embarkation}']"):
                self._record_error("Port of embarkation selection", f"Unable to select port of embarkation '{port_of_embarkation}' from dropdown", "element_interaction_error")
                return False

            self._record_success(f"Selected port of embarkation: {port_of_embarkation}")
            
            # HANDLE INTENDED DATE OF ARRIVAL
            arrival_date_object = self.form_data.intended_arrival_date
            self.logger.info(f"Extracted 'Intended Date of Arrival' from the database: {arrival_date_object}")
        
            if arrival_date_object and isinstance(arrival_date_object, (date, datetime)):
                self.logger.info("Successfully obtained the arrival date object. Moving onto calendar interaction.")

                picker_success = self.intended_arrival_date_picker(arrival_date_object)
                
                if not picker_success:
                    self.logger.error("Calendar date selection failed")
                    return False
                
                self.logger.info("Calendar selection successful.")
            else:
                self.logger.error(f"Couldn't obtain a valid date object from the database: {arrival_date_object}")
                return False

            # HANDLE CLICK NEXT BUTTON
            if not self.element_handler.click_element(self.NEXT_STAGE_BUTTON):
                self.logger.error("Unable to click the 'Next' button to move to stage 2.")
                return False

            ################################
            ####### STAGE 2: PERSONAL ######
            ################################
            self.logger.info("Starting Stage 2: Personal")

            # HANDLE FIRST NAME
            first_name = self.form_data.order.first_name
            self.logger.info(f"Extracted 'First Name' from the database: {first_name}")

            if not self.element_handler.text_field(self.FIRST_NAME_XPATH, first_name):
                self._record_error("First name entry", f"Unable to enter first name '{first_name}' into field", "element_interaction_error")
                return False

            self._record_success(f"Entered first name: {first_name}")

            # HANDLE SURNAME
            surname = self.form_data.order.surname
            self.logger.info(f"Extracted surname from the database: {surname}")

            if not self.element_handler.text_field(self.SURNAME_XPATH, surname):
                self._record_error("Surname entry", f"Unable to enter surname '{surname}' into field", "element_interaction_error")
                return False

            self._record_success(f"Entered surname: {surname}")

            # HANDLE GENDER DROPDOWN
            gender_value_from_db = self.form_data.gender
            self.logger.info(f"Extracted the gender from the database: {gender_value_from_db}")

            actual_gender_option_xpath = self.GENDER_SELECTION_XPATH.format(gender=gender_value_from_db)
            self.logger.info(f"Constructed XPath for gender option: {actual_gender_option_xpath}")

            if not self.element_handler.dropdowns(self.GENDER_DROPDOWN_XPATH, actual_gender_option_xpath): # Pass the constructed XPath
                self._record_error("Gender dropdown selection", f"Unable to select gender '{gender_value_from_db}' from dropdown", "gender_dropdown_error")
                return False

            self._record_success(f"Selected gender: {gender_value_from_db}")
            
            # HANDLE COUNTRY OF BIRTH
            country_of_birth_value = self.form_data.country_of_birth
            self.logger.info(f"Extracted 'Country of Birth' from the database: {country_of_birth_value}")
            
            if not self.element_handler.dropdowns(self.COUNTRY_OF_BIRTH_XPATH, f"//div[@role='option'][@value='{country_of_birth_value}']"):
                self.logger.error(f"Unable to select country of birth ({country_of_birth_value}) from the drop down menu")
                return False
            
            # HANDLE DATE OF BIRTH


########################################################

            # HANDLE NATIONALITY (Bespoke debugging)
            nationality = self.form_data.nationality
            self.logger.info(f"Extracted the nationality from the database: {nationality}")

            if not self.element_handler.dropdowns(self.NATIONALITY_XPATH, f"//div[@role='option'][@value='{nationality}']"):
                self.logger.error(f"Unable to select nationality from the drop down: {nationality}")
                return False


            
            
            
            """
            nationality_value_from_db = self.form_data.nationality
            self.logger.info(f"Extracted the nationality from the database: {nationality_value_from_db}")
            self.logger.info(f"--- Starting Bespoke Debugging for Nationality Dropdown ---")

            # 1. Click the main nationality dropdown trigger to open the options list
            self.logger.info(f"Attempting to click nationality dropdown trigger: {self.NATIONALITY_XPATH}")
            nationality_trigger_element = self.element_handler.click_element(self.NATIONALITY_XPATH)

            if not nationality_trigger_element:
                self.logger.error(f"Failed to click nationality dropdown trigger: {self.NATIONALITY_XPATH}")
                return False # Stop if we can't even open the dropdown
            
            self.logger.info(f"Successfully clicked nationality dropdown trigger. Waiting for options to appear...")
            
            time.sleep(2) # Increase sleep to give ample time for options to render for debugging

            # 2. Find and log available options
            try:
                self.logger.info("--- Debugging: Listing available nationality options ---")
                available_nationality_options = self.driver.find_elements(By.XPATH, "//div[@role='option']") # Generic XPath for options
                
                if available_nationality_options:
                    self.logger.info(f"Found {len(available_nationality_options)} potential nationality option(s):")
                    for i, option_el in enumerate(available_nationality_options):
                        option_text = "N/A"
                        try:
                            span_child = option_el.find_elements(By.XPATH, ".//span")
                            if span_child:
                                option_text = span_child[0].text.strip()
                            else:
                                option_text = option_el.text.strip()
                        except Exception:
                            pass 
                        
                        option_value_attr = option_el.get_attribute('value')
                        is_displayed = option_el.is_displayed()
                        
                        self.logger.info(
                            f"  Nationality Option {i+1}: "
                            f"Text='{option_text}', "
                            f"Value='{option_value_attr}', "
                            f"Displayed={is_displayed}"
                        )
                else:
                    self.logger.info("No potential nationality options found with the XPath: //div[@role='option']")
                self.logger.info("--- End Bespoke Nationality Debugging ---")
            except Exception as debug_ex:
                self.logger.error(f"Error during nationality option debugging: {debug_ex}")
            """

########################################################        


            # HANDLE COUNTRY OF RESIDENCE
            country_of_residence = self.form_data.country_of_residence
            self.logger.info(f"Extracted the country of residence from the database: {country_of_residence}")

            if not self.element_handler.dropdowns(self.COUNTRY_OF_RESIDENCE_XPATH, f"//div[@role='option'][@value='{country_of_residence}']"):
                self.logger.error(f"Unable to select country of residence ({country_of_residence}) from the drop down")
                return False
            
            time.sleep(10)

            ################################
            ######## STAGE 3: TRAVEL #######
            ################################
            self.logger.info("Starting Stage 3: Travel")

                
            ################################
            ##### STAGE 4: DESTINATION #####
            ################################
            self.logger.info("Starting Stage 4: Destination")


            ###################################
            ### SUCCESSFULLY COMPLETED FORM ###
            ###################################
            self._record_success("Form submission completed successfully")
            self.logger.info("Form submission completed successfully.")
            return True

        except Exception as e:
            # Record the exception with context
            error_message = str(e)
            if "timeout" in error_message.lower():
                self._record_error("Page timeout", f"Page loading timeout: {error_message}", "page_timeout_error")
            elif "element" in error_message.lower() and "not found" in error_message.lower():
                self._record_error("Element not found", f"Required element not found: {error_message}", "element_not_found_error")
            elif "webdriver" in error_message.lower():
                self._record_error("WebDriver error", f"Browser/WebDriver error: {error_message}", "webdriver_error")
            else:
                self._record_error("Unexpected error", f"Unexpected error during form submission: {error_message}", "bot_execution_error")

            self.logger.error(f"Unexpected error during form submission: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False

        finally:
            self.cleanup()


# Entry point function for testing
def test_with_real_data():
    import traceback
    from orders.models import BarbadosForm, order

    try:
        # Find the Barbados form with the criminal check status of passed
        barbados_forms = BarbadosForm.objects.filter(order__status="criminal_check_passed")

        if not barbados_forms.exists():
            logging.info("No Barbados form foudn with the criminal check status of 'passed'.")

            # get random order to test
            test_order = order.objects.all()
            if not test_order.exists():
                print("No orders found to test.")
                return False
            
            # use the first order for testing 
            test_order = test_order.first()
            print(f"using test order: {test_order.id} - {test_order.first_name} {test_order.surname}")

            # get associated with BarbadosForm 
            try:
                barbados_form = BarbadosForm.objects.get(order=test_order)
            except BarbadosForm.DoesNotExist:
                print("No Barbados form found for this order.")
                return False
        
        else:
            # Get the first matching form
            barbados_form = barbados_forms.first()

        print(f"Found form for {barbados_form.order.first_name} {barbados_form.order.surname}")

        # Create and run the bot
        bot = BarbadosFormBot(barbados_form)
        result = bot.run()

        if result:
            print("Bot ran successfully.")
        else:
            print("Bot failed to run.")
        return result
    
    except Exception as e:
        print(f"error setting up test; {e}")
        print(traceback.format_exc())
        return False
    

# For use in signals.py - maintains the same interface as the original
def run_barbados_bot(form_data_instance):
    """Main entry point to run the Barbados bot - maintains compatibility with original."""
    bot = BarbadosFormBot(form_data_instance)
    success = bot.run()

    # Return both success status and error information
    return {
        'success': success,
        'error_details': bot.error_details,
        'error_summary': bot.get_error_summary(),
        'last_error': bot.last_error,
        'failed_step': bot.failed_step
    }

# Backward compatibility function that only returns boolean
def run_barbados_bot_simple(form_data_instance):
    """Simple version that only returns boolean for backward compatibility."""
    bot = BarbadosFormBot(form_data_instance)
    return bot.run()


if __name__ == "__main__":
    test_with_real_data()