# This should be in public_html location of server

# DO NOT REMOVE. CLOUD<PERSON><PERSON>UX PASSENGER CONFIGURATION BEGIN
PassengerAppRoot "/home/<USER>/JG-Innovations--master"
PassengerBaseURI "/"
PassengerPython "/home/<USER>/virtualenv/JG-Innovations--master/3.9/bin/python"
# DO NOT REMOVE. CLOUDLINUX PASSENGER CONFIGURATION END
PassengerEnabled on
PassengerAppEnv production
PassengerBaseURI /
PassengerPython /home/<USER>/virtualenv/JG-Innovations--master/3.9/bin/python

RewriteEngine On

SetEnv DJANGO_SETTINGS_MODULE=config.settings

RewriteEngine On

# Explicitly serve existing static files
RewriteCond %{REQUEST_URI} ^/static/ [OR]
RewriteCond %{REQUEST_URI} ^/media/
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^ - [L]
