import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Sidebar from '../shared/Sidebar';
import {
    Container,
    Typography,
    TextField,
    // Button, // No longer directly needed from MUI here
    CircularProgress,
    Alert,
    Select,
    MenuItem,
    InputLabel,
    FormControl,
    Box
} from '@mui/material';
import CustomButton from '../shared/Custom_button'; // Import CustomButton

function EditOrder() {
    const { id } = useParams();
    const navigate = useNavigate();

    const [first_name, setFirstName] = useState('');
    const [surname, setSurname] = useState('');
    const [customerEmail, setCustomerEmail] = useState('');
    const [location, setLocation] = useState('');
    const [status, setStatus] = useState('');
    const [qrFile, setQrFile] = useState(null);
    const [currentQrCodeUrl, setCurrentQrCodeUrl] = useState('');
    const [createdAt, setCreatedAt] = useState('');
    const [updatedAt, setUpdatedAt] = useState('');
    const [orderStatuses, setOrderStatuses] = useState([]);

    const [availableLocations, setAvailableLocations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [submitError, setSubmitError] = useState(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        async function fetchData() {
            setLoading(true);
            try {
                const locRes = await fetch('/api/locations/', { credentials: 'include', headers: { 'Accept': 'application/json' } });
                if (!locRes.ok) throw new Error('Failed to fetch locations');
                const locData = await locRes.json();
                setAvailableLocations(Array.isArray(locData.results) ? locData.results : (Array.isArray(locData) ? locData : []));

                const orderRes = await fetch(`/api/orders/${id}/`, { credentials: 'include', headers: { 'Accept': 'application/json' } });
                if (!orderRes.ok) throw new Error('Failed to fetch order');
                const orderData = await orderRes.json();

                setFirstName(orderData.first_name || '');
                setSurname(orderData.surname || '');
                setCustomerEmail(orderData.customer_email || '');
                setLocation(orderData.location || '');
                setStatus(orderData.status || '');
                setCurrentQrCodeUrl(orderData.qr_code || '');
                setCreatedAt(orderData.created_at ? new Date(order.created_at).toLocaleString('en-US', dateOptions) + ' (UTC)' : '');
                setUpdatedAt(orderData.updated_at ?new Date(order.updated_at).toLocaleString('en-US', dateOptions) + ' (UTC)' : '');

            } catch (error) {
                setSubmitError(error.message);
            } finally {
                setLoading(false);
            }
        }
        fetchData();
    }, [id]);

    const handleFileChange = (e) => {
        setQrFile(e.target.files[0]);
        if (e.target.files[0]) {
            setCurrentQrCodeUrl('');
        }
    };

    useEffect(() => {
        async function fetchStatusChoices() {
            try {
                const response = await fetch('/api/order_status_choices/', {
                    credentials: 'include',
                    headers: { 'Accept': 'application/json' }
                });
                if (!response.ok) {
                    throw new Error(`Failed to fetch order statuses (Status: ${response.status})`);
                }
                const data = await response.json();
                setOrderStatuses(data);
            } catch (error) {
                console.error("Error fetching status choices:", error);
            }
        }
        fetchStatusChoices();
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        setSubmitError(null);
        const formData = new FormData();
        formData.append('first_name', first_name);
        formData.append('surname', surname);
        formData.append('customer_email', customerEmail);
        formData.append('location', location);
        formData.append('status', status);
        if (qrFile) {
            formData.append('qr_code', qrFile);
        }

        try {
            const response = await fetch(`/api/orders/${id}/`, {
                method: 'PATCH',
                headers: { 'X-CSRFToken': getCookie('csrftoken') },
                credentials: 'include',
                body: formData
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: `Update failed (Status: ${response.status})` }));
                throw new Error(errorData.detail || `Update failed`);
            }
            navigate('/orders', { state: { message: 'Order updated successfully!' } });
        } catch (error) {
            setSubmitError(error.message);
            setIsSubmitting(false);
        }
    };

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    if (loading) {
        return (
            <Box sx={{ display: 'flex' }}>
                <Sidebar />
                <Container maxWidth="sm" sx={{ mt: 4, mb: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', flexGrow: 1 }}>
                    <CircularProgress />
                </Container>
            </Box>
        );
    }

    return (
        <Box sx={{ display: 'flex' }}>
            <Sidebar />
            <Container maxWidth="sm" sx={{ mt: 4, mb: 4, flexGrow: 1 }}>
                <Typography variant="h5" component="h1" gutterBottom align="center" fontWeight="bold">
                    Edit Order
                </Typography>
                {submitError && <Alert severity="error" sx={{ mb: 2 }}>{submitError}</Alert>}
                <Box component="form" onSubmit={handleSubmit} noValidate>
                    <TextField
                        label="First Name"
                        value={first_name}
                        onChange={(e) => setFirstName(e.target.value)}
                        fullWidth
                        required
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />
                    <TextField
                        label="Surname"
                        value={surname}
                        onChange={(e) => setSurname(e.target.value)}
                        fullWidth
                        required
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />
                    <TextField
                        label="Customer Email"
                        type="email"
                        value={customerEmail}
                        onChange={(e) => setCustomerEmail(e.target.value)}
                        fullWidth
                        required
                        sx={{ mb: 2 }}
                        disabled={isSubmitting}
                    />

                    <Box sx={{ mt: 2, mb: 1 }}>
                        <CustomButton
                            variant="outlined"
                            component="label"
                            fullWidth
                            disabled={isSubmitting}
                            sx={{ mb: (qrFile || currentQrCodeUrl) ? 0.5 : 2 }}
                        >
                            {qrFile ? `Change QR Code` : (currentQrCodeUrl ? 'Change QR Code' : 'Upload QR Code')}
                            <input type="file" hidden onChange={handleFileChange} accept="image/*"/>
                        </CustomButton>
                        {qrFile && (
                            <Typography variant="caption" display="block" sx={{ mt: 0.5, textAlign: 'center' }}>
                                New file: {qrFile.name}
                            </Typography>
                        )}
                        {!qrFile && currentQrCodeUrl && (
                             <Box sx={{ mt: 1, textAlign: 'center' }}>
                                <Typography variant="caption" display="block" sx={{ mb: 0.5 }}>
                                    Current QR Code:
                                </Typography>
                                <img src={currentQrCodeUrl} alt="Current QR Code" style={{ maxWidth: '100px', maxHeight: '100px', border: '1px solid #ccc' }} />
                            </Box>
                        )}
                    </Box>

                    <FormControl fullWidth required sx={{ mb: 2 }} disabled={isSubmitting}>
                        <InputLabel>Status</InputLabel>
                        <Select
                            value={status}
                            label="Status"
                            onChange={(e) => setStatus(e.target.value)}
                        >
                            {orderStatuses.length === 0 && (
                                <MenuItem value="" disabled>
                                    <em>Loading statuses...</em>
                                </MenuItem>
                            )}
                            {orderStatuses.map((item) => (
                                <MenuItem key={item.value} value={item.value}>
                                    {item.display}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <FormControl fullWidth required sx={{ mb: 2 }} disabled={isSubmitting || availableLocations.length === 0}>
                        <InputLabel>Location</InputLabel>
                        <Select
                            value={location}
                            label="Location"
                            onChange={(e) => setLocation(e.target.value)}
                        >
                            {availableLocations.length === 0 && <MenuItem value="" disabled><em>Loading locations...</em></MenuItem>}
                            {availableLocations.map((loc) => (
                                <MenuItem key={loc.id} value={loc.id}>{loc.location_name || loc.name}</MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    {createdAt && (
                        <TextField
                            label="Created At"
                            value={createdAt}
                            fullWidth
                            disabled
                            sx={{ mb: 2 }}
                            InputLabelProps={{ shrink: true }}
                        />
                    )}
                    {updatedAt && (
                        <TextField
                            label="Updated At"
                            value={updatedAt}
                            fullWidth
                            disabled
                            sx={{ mb: 2 }}
                            InputLabelProps={{ shrink: true }}
                        />
                    )}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                        <CustomButton
                            variant="outlined"
                            onClick={() => navigate('/orders')}
                            disabled={isSubmitting}
                            sx={{ p: 1.5 }}
                        >
                            Cancel
                        </CustomButton>
                        <CustomButton
                            type="submit"
                            variant="contained"
                            disabled={isSubmitting}
                            sx={{ p: 1.5 }}
                        >
                            {isSubmitting ? <CircularProgress size={24} /> : 'Save Changes'}
                        </CustomButton>
                    </Box>
                </Box>
            </Container>
        </Box>
    );
}

export default EditOrder;