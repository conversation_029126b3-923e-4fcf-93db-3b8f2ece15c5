from django.contrib import admin
from .models import Location
from orders.models import order

class OrderInline(admin.TabularInline):
    model = order
    extra = 0

    list_display = ('first_name', 'surname', 'customer_email', 'status', 'created_at', 'updated_at')
    list_filter = ('status',)
    can_delete = False
    readonly_fields = ('first_name', 'surname', 'customer_email', 'status', 'created_at', 'updated_at')

@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('id', 'location_name', 'created_at', 'updated_at')
    search_fields = ('location_name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [OrderInline]
