from django.db import models
from locations.models import Location
import uuid


class order(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    first_name = models.Char<PERSON><PERSON>(max_length=100)
    surname = models.CharField(max_length=100)
    customer_email = models.CharField(max_length=100)
    qr_code = models.FileField(upload_to='qr_codes/', blank=True, null=True)
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('qr_generated', 'QR Generated'),
        ('criminal_check_passed', 'Criminal Check Passed'),
        ('criminal_check_failed', 'Criminal Check Failed'),
        ('bot_submission_retry', 'Bot Submission Retry'),
        ('bot_submission_failed', 'Bot Submission Failed'),
        ('bot_completed_form', 'Bot Completed Form'),
        ('refunded', 'Refunded'),
        ('completed', 'Completed'),
    ]
    status = models.Char<PERSON><PERSON>(max_length=25, choices=STATUS_CHOICES, default='pending') ## made status editable=False
    
    location = models.ForeignKey(Location, on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
        
    def __str__(self):            
        return f"{self.id} - {self.first_name} - {self.surname} - {self.customer_email} - {self.location.location_name} - {self.created_at} - {self.status}"

# CriminalCheck------------------------------------------------------------
class CriminalCheckDatabase(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Criminal Check Database"
        verbose_name_plural = "Criminal Check Databases"

class CriminalCheck(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(order, on_delete=models.CASCADE)
    database = models.ForeignKey(CriminalCheckDatabase, on_delete=models.PROTECT)
    
    CHECK_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('passed', 'Passed'),
        ('failed', 'Failed'),
        ('error', 'Something went wrong with the search'),
    ]
    check_status = models.CharField(max_length=20, choices=CHECK_STATUS_CHOICES, default='pending')
    
    checked_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Check for {self.order.first_name} - {self.order.surname} - {self.database.name} - {self.check_status}"

    class Meta:
        unique_together = ['order', 'database']




# FailedSubmission model ------------------------------------------------

class FailedSubmission(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(order, on_delete=models.CASCADE, related_name='failed_submissions')
    error_message = models.TextField()
    retry_attempts = models.IntegerField(default=0)
    last_attempt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Failed submission for order {self.order.id} - Attempts: {self.retry_attempts}"




# Barbados model ------------------------------------------------

class BarbadosForm(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.OneToOneField(order, on_delete=models.CASCADE, related_name='barbados_form')
    
    residential_choices = [
        ('Permanent Address Is In Barbados', 'Permanent Address Is In Barbados'),
        ('Non-Resident/Visitor', 'Non-Resident/Visitor')
    ]
    residential_status = models.CharField(max_length=100, choices=residential_choices)

    how_are_you_entering_choices = [
        ('Air', 'Air'),
        ('Sea', 'Sea')
    ]
    how_are_you_entering = models.CharField(max_length=100, choices=how_are_you_entering_choices)
    
    airline = models.CharField(max_length=100, null=True, blank=True)
    flight_number = models.CharField(max_length=50, null=True, blank=True)
    vessel_name = models.CharField(max_length=100, null=True, blank=True)
    vessel_number = models.CharField(max_length=100, null=True, blank=True)
    
    departure_country_choices = [
        ('AFG', 'Afghanistan'),
        ('ALA', 'Åland Islands'),
        ('ALB', 'Albania'),
        ('DZA', 'Algeria'),
        ('ASM', 'American Samoa'),
        ('AND', 'Andorra'),
        ('AGO', 'Angola'),
        ('AIA', 'Anguilla'),
        ('ATA', 'Antarctica'),
        ('ATG', 'Antigua and Barbuda'),
        ('ARG', 'Argentina'),
        ('ARM', 'Armenia'),
        ('ABW', 'Aruba'),
        ('AUS', 'Australia'),
        ('AUT', 'Austria'),
        ('AZE', 'Azerbaijan'),
        ('BHS', 'Bahamas'),
        ('BHR', 'Bahrain'),
        ('BGD', 'Bangladesh'),
        ('BLR', 'Belarus'),
        ('BEL', 'Belgium'),
        ('BLZ', 'Belize'),
        ('BEN', 'Benin'),
        ('BMU', 'Bermuda'),
        ('BTN', 'Bhutan'),
        ('BOL', 'Bolivia'),
        ('BIH', 'Bosnia and Herzegovina'),
        ('BWA', 'Botswana'),
        ('BVT', 'Bouvet Island'),
        ('BRA', 'Brazil'),
        ('IOT', 'British Indian Ocean Territory'),
        ('VGB', 'British Virgin Islands'),
        ('BRN', 'Brunei'),
        ('BGR', 'Bulgaria'),
        ('BFA', 'Burkina Faso'),
        ('BDI', 'Burundi'),
        ('KHM', 'Cambodia'),
        ('CMR', 'Cameroon'),
        ('CAN', 'Canada'),
        ('CPV', 'Cape Verde'),
        ('BES', 'Caribbean Netherlands'),
        ('CYM', 'Cayman Islands'),
        ('CAF', 'Central African Republic'),
        ('TCD', 'Chad'),
        ('CHL', 'Chile'),
        ('CHN', 'China'),
        ('CXR', 'Christmas Island'),
        ('CCK', 'Cocos (Keeling) Islands'),
        ('COL', 'Colombia'),
        ('COM', 'Comoros'),
        ('COK', 'Cook Islands'),
        ('CRI', 'Costa Rica'),
        ('HRV', 'Croatia'),
        ('CUB', 'Cuba'),
        ('CUW', 'Curaçao'),
        ('CYP', 'Cyprus'),
        ('CZE', 'Czechia'),
        ('DNK', 'Denmark'),
        ('DJI', 'Djibouti'),
        ('DMA', 'Dominica'),
        ('DOM', 'Dominican Republic'),
        ('COD', 'DR Congo'),
        ('ECU', 'Ecuador'),
        ('EGY', 'Egypt'),
        ('SLV', 'El Salvador'),
        ('GNQ', 'Equatorial Guinea'),
        ('ERI', 'Eritrea'),
        ('EST', 'Estonia'),
        ('SWZ', 'Eswatini'),
        ('ETH', 'Ethiopia'),
        ('FLK', 'Falkland Islands'),
        ('FRO', 'Faroe Islands'),
        ('FJI', 'Fiji'),
        ('FIN', 'Finland'),
        ('FRA', 'France'),
        ('GUF', 'French Guiana'),
        ('PYF', 'French Polynesia'),
        ('ATF', 'French Southern and Antarctic Lands'),
        ('GAB', 'Gabon'),
        ('GMB', 'Gambia'),
        ('GEO', 'Georgia'),
        ('DEU', 'Germany'),
        ('GHA', 'Ghana'),
        ('GIB', 'Gibraltar'),
        ('GRC', 'Greece'),
        ('GRL', 'Greenland'),
        ('GRD', 'Grenada'),
        ('GLP', 'Guadeloupe'),
        ('GUM', 'Guam'),
        ('GTM', 'Guatemala'),
        ('GGY', 'Guernsey'),
        ('GIN', 'Guinea'),
        ('GNB', 'Guinea-Bissau'),
        ('GUY', 'Guyana'),
        ('HTI', 'Haiti'),
        ('HMD', 'Heard Island and McDonald Islands'),
        ('HND', 'Honduras'),
        ('HKG', 'Hong Kong'),
        ('HUN', 'Hungary'),
        ('ISL', 'Iceland'),
        ('IND', 'India'),
        ('IDN', 'Indonesia'),
        ('IRN', 'Iran'),
        ('IRQ', 'Iraq'),
        ('IRL', 'Ireland'),
        ('IMN', 'Isle of Man'),
        ('ISR', 'Israel'),
        ('ITA', 'Italy'),
        ('CIV', 'Ivory Coast'),
        ('JAM', 'Jamaica'),
        ('JPN', 'Japan'),
        ('JEY', 'Jersey'),
        ('JOR', 'Jordan'),
        ('KAZ', 'Kazakhstan'),
        ('KEN', 'Kenya'),
        ('KIR', 'Kiribati'),
        ('UNK', 'Kosovo'),
        ('KWT', 'Kuwait'),
        ('KGZ', 'Kyrgyzstan'),
        ('LAO', 'Laos'),
        ('LVA', 'Latvia'),
        ('LBN', 'Lebanon'),
        ('LSO', 'Lesotho'),
        ('LBR', 'Liberia'),
        ('LBY', 'Libya'),
        ('LIE', 'Liechtenstein'),
        ('LTU', 'Lithuania'),
        ('LUX', 'Luxembourg'),
        ('MAC', 'Macau'),
        ('MDG', 'Madagascar'),
        ('MWI', 'Malawi'),
        ('MYS', 'Malaysia'),
        ('MDV', 'Maldives'),
        ('MLI', 'Mali'),
        ('MLT', 'Malta'),
        ('MHL', 'Marshall Islands'),
        ('MTQ', 'Martinique'),
        ('MRT', 'Mauritania'),
        ('MUS', 'Mauritius'),
        ('MYT', 'Mayotte'),
        ('MEX', 'Mexico'),
        ('FSM', 'Micronesia'),
        ('MDA', 'Moldova'),
        ('MCO', 'Monaco'),
        ('MNG', 'Mongolia'),
        ('MNE', 'Montenegro'),
        ('MSR', 'Montserrat'),
        ('MAR', 'Morocco'),
        ('MOZ', 'Mozambique'),
        ('MMR', 'Myanmar'),
        ('NAM', 'Namibia'),
        ('NRU', 'Nauru'),
        ('NPL', 'Nepal'),
        ('NLD', 'Netherlands'),
        ('NCL', 'New Caledonia'),
        ('NZL', 'New Zealand'),
        ('NIC', 'Nicaragua'),
        ('NER', 'Niger'),
        ('NGA', 'Nigeria'),
        ('NIU', 'Niue'),
        ('NFK', 'Norfolk Island'),
        ('PRK', 'North Korea'),
        ('MKD', 'North Macedonia'),
        ('MNP', 'Northern Mariana Islands'),
        ('NOR', 'Norway'),
        ('OMN', 'Oman'),
        ('PAK', 'Pakistan'),
        ('PLW', 'Palau'),
        ('PSE', 'Palestine'),
        ('PAN', 'Panama'),
        ('PNG', 'Papua New Guinea'),
        ('PRY', 'Paraguay'),
        ('PER', 'Peru'),
        ('PHL', 'Philippines'),
        ('PCN', 'Pitcairn Islands'),
        ('POL', 'Poland'),
        ('PRT', 'Portugal'),
        ('PRI', 'Puerto Rico'),
        ('QAT', 'Qatar'),
        ('COG', 'Republic of the Congo'),
        ('REU', 'Réunion'),
        ('ROU', 'Romania'),
        ('RUS', 'Russia'),
        ('RWA', 'Rwanda'),
        ('BLM', 'Saint Barthélemy'),
        ('SHN', 'Saint Helena, Ascension and Tristan da Cunha'),
        ('KNA', 'Saint Kitts and Nevis'),
        ('LCA', 'Saint Lucia'),
        ('MAF', 'Saint Martin'),
        ('SPM', 'Saint Pierre and Miquelon'),
        ('VCT', 'Saint Vincent and the Grenadines'),
        ('WSM', 'Samoa'),
        ('SMR', 'San Marino'),
        ('STP', 'São Tomé and Príncipe'),
        ('SAU', 'Saudi Arabia'),
        ('SEN', 'Senegal'),
        ('SRB', 'Serbia'),
        ('SYC', 'Seychelles'),
        ('SLE', 'Sierra Leone'),
        ('SGP', 'Singapore'),
        ('SXM', 'Sint Maarten'),
        ('SVK', 'Slovakia'),
        ('SVN', 'Slovenia'),
        ('SLB', 'Solomon Islands'),
        ('SOM', 'Somalia'),
        ('ZAF', 'South Africa'),
        ('SGS', 'South Georgia'),
        ('KOR', 'South Korea'),
        ('SSD', 'South Sudan'),
        ('ESP', 'Spain'),
        ('LKA', 'Sri Lanka'),
        ('SDN', 'Sudan'),
        ('SUR', 'Suriname'),
        ('SJM', 'Svalbard and Jan Mayen'),
        ('SWE', 'Sweden'),
        ('CHE', 'Switzerland'),
        ('SYR', 'Syria'),
        ('TWN', 'Taiwan'),
        ('TJK', 'Tajikistan'),
        ('TZA', 'Tanzania'),
        ('THA', 'Thailand'),
        ('TLS', 'Timor-Leste'),
        ('TGO', 'Togo'),
        ('TKL', 'Tokelau'),
        ('TON', 'Tonga'),
        ('TTO', 'Trinidad and Tobago'),
        ('TUN', 'Tunisia'),
        ('TUR', 'Turkey'),
        ('TKM', 'Turkmenistan'),
        ('TCA', 'Turks and Caicos Islands'),
        ('TUV', 'Tuvalu'),
        ('UGA', 'Uganda'),
        ('UKR', 'Ukraine'),
        ('ARE', 'United Arab Emirates'),
        ('GBR', 'United Kingdom'),
        ('USA', 'United States'),
        ('UMI', 'United States Minor Outlying Islands'),
        ('VIR', 'United States Virgin Islands'),
        ('URY', 'Uruguay'),
        ('UZB', 'Uzbekistan'),
        ('VUT', 'Vanuatu'),
        ('VAT', 'Vatican City'),
        ('VEN', 'Venezuela'),
        ('VNM', 'Vietnam'),
        ('WLF', 'Wallis and Futuna'),
        ('ESH', 'Western Sahara'),
        ('YEM', 'Yemen'),
        ('ZMB', 'Zambia'),
        ('ZWE', 'Zimbabwe')
    ]
    departure_country = models.CharField(max_length=100, choices=departure_country_choices)
    
    departure_airport_choices = [
        ('ABZ', 'ABERDEEN DYCE AIRPORT'),
        ('VLY', 'ANGLESEY AIRPORT'),
        ('BOL', 'BALLY KELLY AIRPORT'),
        ('BRR', 'BARRA AIRPORT'),
        ('BWF', 'BARROW WALNEY ISLAND AIRPORT'),
        ('BFS', 'BELFAST INTERNATIONAL AIRPORT'),
        ('BBP', 'BEMBRIDGE AIRPORT'),
        ('BEB', 'BENBECULA AIRPORT'),
        ('GSY', 'BINBROOK AIRFIELD'),
        ('BHX', 'BIRMINGHAM INTERNATIONAL AIRPORT'),
        ('BBS', 'BLACKBUSHE AIRPORT'),
        ('BLK', 'BLACKPOOL INTERNATIONAL AIRPORT'),
        ('BOH', 'BOURNEMOUTH AIRPORT'),
        ('BRF', 'BRADFORD AIRPORT'),
        ('BSH', 'BRIGHTON AIRPORT'),
        ('BRS', 'BRISTOL AIRPORT'),
        ('FZO', 'BRISTOL FILTON AIRPORT'),
        ('SKL', 'BROADFORD AIRPORT'),
        ('CBG', 'CAMBRIDGE AIRPORT'),
        ('CAL', 'CAMPBELTOWN AIRPORT'),
        ('CWL', 'CARDIFF INTERNATIONAL AIRPORT'),
        ('CAX', 'CARLISLE AIRPORT'),
        ('LDY', 'CITY OF DERRY AIRPORT'),
        ('COL', 'COLL AIRPORT'),
        ('CSA', 'COLONSAY AIRSTRIP'),
        ('GBA', 'COTSWOLD AIRPORT'),
        ('CVT', 'COVENTRY AIRPORT'),
        ('CRN', 'CROMARTY AIRPORT'),
        ('DOC', 'DORNOCH AIRFIELD'),
        ('DND', 'DUNDEE AIRPORT'),
        ('EMA', 'EAST MIDLANDS AIRPORT'),
        ('EOI', 'EDAY AIRPORT'),
        ('EDI', 'EDINBURGH AIRPORT'),
        ('ENK', 'ENNISKILLEN/ST ANGELO AIRPORT'),
        ('EXT', 'EXETER INTERNATIONAL AIRPORT'),
        ('FIE', 'FAIR ISLE AIRPORT'),
        ('FAB', 'FARNBOROUGH AIRPORT'),
        ('FEA', 'FETLAR AIRPORT'),
        ('PME', 'FLEETLANDS HELIPORT'),
        ('FLH', 'FLOTTA ISLE AIRPORT'),
        ('FWM', 'FORT WILLIAMHELIPORT'),
        ('FOA', 'FOULA AIRFIELD'),
        ('BHD', 'GEORGE BEST BELFAST CITY AIRPORT'),
        ('GLA', 'GLASGOW INTERNATIONAL AIRPORT'),
        ('PIK', 'GLASGOW PRESTWICK AIRPORT'),
        ('GLO', 'GLOUCESTERSHIRE AIRPORT'),
        ('QUG', 'GOODWOOD AERODROME'),
        ('HTF', 'HATFIELD AIRPORT'),
        ('HAW', 'HAVERFORDWEST AIRPORT'),
        ('CEG', 'HAWARDEN AIRPORT'),
        ('HEN', 'HENDON AIRPORT'),
        ('HLY', 'HOLYHEAD AIRPORT'),
        ('HOY', 'HOY/LONGHOPE AIRFIELD'),
        ('HUY', 'HUMBERSIDE AIRPORT'),
        ('INV', 'INVERNESS AIRPORT'),
        ('IPW', 'IPSWICH AIRPORT'),
        ('ILY', 'ISLAY AIRPORT'),
        ('MSE', 'KENT INTERNATIONAL AIRPORT (MANSTON)'),
        ('KOI', 'KIRKWALL AIRPORT'),
        ('LEQ', 'LAND\'S END AIRPORT'),
        ('QLA', 'LASHAM'),
        ('LBA', 'LEEDS BRADFORD AIRPORT'),
        ('LWK', 'LERWICK / TINGWALL AIRPORT'),
        ('ADX', 'LEUCHARS STATION AIRFIELD'),
        ('LPL', 'LIVERPOOL JOHN LENNON AIRPORT'),
        ('LPH', 'LOCHGILPHEAD HELIPORT'),
        ('BQH', 'LONDON BIGGIN HILL AIRPORT'),
        ('LCY', 'LONDON CITY AIRPORT'),
        ('LGW', 'LONDON GATWICK AIRPORT'),
        ('LHR', 'LONDON HEATHROW AIRPORT'),
        ('LTN', 'LONDON LUTON AIRPORT'),
        ('QQP', 'LONDON PADDINGTON'),
        ('STN', 'LONDON STANSTED AIRPORT'),
        ('LYX', 'LYDD AIRPORT'),
        ('LYM', 'LYMPNE AIRPORT'),
        ('GQJ', 'MACHRIHANISH AIRPORT'),
        ('MAN', 'MANCHESTER AIRPORT'),
        ('QQM', 'MANCHESTER PICCADILLY'),
        ('KYN', 'MILTON KEYNES AIRPORT'),
        ('ULL', 'MULL AIRPORT'),
        ('NCL', 'NEWCASTLE AIRPORT'),
        ('NQY', 'NEWQUAY CORNWALL AIRPORT'),
        ('NRL', 'NORTH RONALDSAY AIRPORT'),
        ('NWI', 'NORWICH INTERNATIONAL AIRPORT'),
        ('NQT', 'NOTTINGHAM AIRPORT'),
        ('OBN', 'OBAN AIRPORT'),
        ('OHP', 'OBAN HELIPORT'),
        ('OUK', 'OUT SKERRIES AIRFIELD'),
        ('OXF', 'OXFORD (KIDLINGTON) AIRPORT'),
        ('PSV', 'PAPA STOUR AIRPORT'),
        ('PPW', 'PAPA WESTRAY AIRPORT'),
        ('PZE', 'PENZANCE HELIPORT'),
        ('PSL', 'PERTH/SCONE AIRPORT'),
        ('PLH', 'PLYMOUTH CITY AIRPORT'),
        ('AYH', 'RAF ALCONBURY'),
        ('BEX', 'RAF BENSON'),
        ('BWY', 'RAF BENTWATERS'),
        ('BZZ', 'RAF BRIZE NORTON'),
        ('CLF', 'RAF COLTISHALL'),
        ('QCY', 'RAF CONINGSBY'),
        ('OKH', 'RAF COTTESMORE'),
        ('FFD', 'RAF FAIRFORD'),
        ('EWY', 'RAF GREENHAM COMMON'),
        ('BEQ', 'RAF HONINGTON'),
        ('FSS', 'RAF KINLOSS'),
        ('LKZ', 'RAF LAKENHEATH'),
        ('HRT', 'RAF LINTON-ON-OUSE'),
        ('LMO', 'RAF LOSSIEMOUTH'),
        ('LYE', 'RAF LYNEHAM'),
        ('KNF', 'RAF MARHAM'),
        ('MHZ', 'RAF MILDENHALL'),
        ('NHT', 'RAF NORTHOLT'),
        ('ODH', 'RAF ODIHAM'),
        ('SQZ', 'RAF SCAMPTON'),
        ('UHF', 'RAF UPPER HEYFORD'),
        ('WTN', 'RAF WADDINGTON'),
        ('WEM', 'RAF WEST MALLING'),
        ('QUY', 'RAF WYTON'),
        ('KRH', 'REDHILL AERODROME'),
        ('YEO', 'RNAS YEOVILTON'),
        ('DSA', 'ROBIN HOOD DONCASTER SHEFFIELD AIRPORT'),
        ('RCS', 'ROCHESTER AIRPORT'),
        ('RAY', 'ROTHSAY HELIPORT'),
        ('NDY', 'SANDAY AIRPORT'),
        ('SCS', 'SCATSTA AIRPORT'),
        ('FKH', 'SCULTHORP ROYAL AIR FORCE'),
        ('SZD', 'SHEFFIELD CITY AIRPORT'),
        ('ESH', 'SHOREHAM AIRPORT'),
        ('SOU', 'SOUTHAMPTON AIRPORT'),
        ('SEN', 'SOUTHEND AIRPORT'),
        ('ISC', 'ST. MARY\'S AIRPORT'),
        ('SYY', 'STORNOWAY AIRPORT'),
        ('SOY', 'STRONSAY AIRPORT'),
        ('LSI', 'SUMBURGH AIRPORT'),
        ('SWS', 'SWANSEA AIRPORT'),
        ('SWI', 'SWINDON AIRPORT'),
        ('ORM', 'SYWELL AERODROME'),
        ('MME', 'TEESSIDE INTERNATIONAL AIRPORT'),
        ('TRE', 'TIREE AIRPORT'),
        ('TTK', 'TOTTENHAM HALE STATION'),
        ('TSO', 'TRESCO AIRPORT'),
        ('UNT', 'UNST AIRPORT'),
        ('UPV', 'UPAVON AERODROME'),
        ('WRT', 'WARTON AERODROME'),
        ('WRY', 'WESTRAY AIRPORT'),
        ('WXF', 'WETHER FIELD ROYAL AIR FORCE'),
        ('WHS', 'WHALSAY AIRSTRIP'),
        ('WIC', 'WICK AIRPORT'),
        ('WOB', 'WOODBRIDGE RAF'),
        ('WFD', 'WOODFORD AIRPORT'),
        ('HYC', 'WYCOMBE AIR PARK')   
    ]
    departure_airport = models.CharField(max_length=100, choices=departure_airport_choices)
    
    intended_arrival_date = models.DateField()
    
    gender_choices = [
        ('MALE', 'MALE'),
        ('FEMALE', 'FEMALE')
    ]
    gender = models.CharField(max_length=100, choices=gender_choices)
    
    country_of_birth_choices = [
        ('AFG', 'AFGHANISTAN'),
        ('ALA', 'ÅLAND ISLANDS'),
        ('ALB', 'ALBANIA'),
        ('DZA', 'ALGERIA'),
        ('ASM', 'AMERICAN SAMOA'),
        ('AND', 'ANDORRA'),
        ('AGO', 'ANGOLA'),
        ('AIA', 'ANGUILLA'),
        ('ATA', 'ANTARCTICA'),
        ('ATG', 'ANTIGUA AND BARBUDA'),
        ('ARG', 'ARGENTINA'),
        ('ARM', 'ARMENIA'),
        ('ABW', 'ARUBA'),
        ('AUS', 'AUSTRALIA'),
        ('AUT', 'AUSTRIA'),
        ('AZE', 'AZERBAIJAN'),
        ('BHS', 'BAHAMAS'),
        ('BHR', 'BAHRAIN'),
        ('BGD', 'BANGLADESH'),
        ('BRB', 'BARBADOS'),
        ('BLR', 'BELARUS'),
        ('BEL', 'BELGIUM'),
        ('BLZ', 'BELIZE'),
        ('BEN', 'BENIN'),
        ('BMU', 'BERMUDA'),
        ('BTN', 'BHUTAN'),
        ('BOL', 'BOLIVIA'),
        ('BIH', 'BOSNIA AND HERZEGOVINA'),
        ('BWA', 'BOTSWANA'),
        ('BVT', 'BOUVET ISLAND'),
        ('BRA', 'BRAZIL'),
        ('IOT', 'BRITISH INDIAN OCEAN TERRITORY'),
        ('VGB', 'BRITISH VIRGIN ISLANDS'),
        ('BRN', 'BRUNEI'),
        ('BGR', 'BULGARIA'),
        ('BFA', 'BURKINA FASO'),
        ('BDI', 'BURUNDI'),
        ('KHM', 'CAMBODIA'),
        ('CMR', 'CAMEROON'),
        ('CAN', 'CANADA'),
        ('CPV', 'CAPE VERDE'),
        ('BES', 'CARIBBEAN NETHERLANDS'),
        ('CYM', 'CAYMAN ISLANDS'),
        ('CAF', 'CENTRAL AFRICAN REPUBLIC'),
        ('TCD', 'CHAD'),
        ('CHL', 'CHILE'),
        ('CHN', 'CHINA'),
        ('CXR', 'CHRISTMAS ISLAND'),
        ('CCK', 'COCOS (KEELING) ISLANDS'),
        ('COL', 'COLOMBIA'),
        ('COM', 'COMOROS'),
        ('COK', 'COOK ISLANDS'),
        ('CRI', 'COSTA RICA'),
        ('HRV', 'CROATIA'),
        ('CUB', 'CUBA'),
        ('CUW', 'CURAÇAO'),
        ('CYP', 'CYPRUS'),
        ('CZE', 'CZECHIA'),
        ('DNK', 'DENMARK'),
        ('DJI', 'DJIBOUTI'),
        ('DMA', 'DOMINICA'),
        ('DOM', 'DOMINICAN REPUBLIC'),
        ('COD', 'DR CONGO'),
        ('ECU', 'ECUADOR'),
        ('EGY', 'EGYPT'),
        ('SLV', 'EL SALVADOR'),
        ('GNQ', 'EQUATORIAL GUINEA'),
        ('ERI', 'ERITREA'),
        ('EST', 'ESTONIA'),
        ('SWZ', 'ESWATINI'),
        ('ETH', 'ETHIOPIA'),
        ('FLK', 'FALKLAND ISLANDS'),
        ('FRO', 'FAROE ISLANDS'),
        ('FJI', 'FIJI'),
        ('FIN', 'FINLAND'),
        ('FRA', 'FRANCE'),
        ('GUF', 'FRENCH GUIANA'),
        ('PYF', 'FRENCH POLYNESIA'),
        ('ATF', 'FRENCH SOUTHERN AND ANTARCTIC LANDS'),
        ('GAB', 'GABON'),
        ('GMB', 'GAMBIA'),
        ('GEO', 'GEORGIA'),
        ('DEU', 'GERMANY'),
        ('GHA', 'GHANA'),
        ('GIB', 'GIBRALTAR'),
        ('GRC', 'GREECE'),
        ('GRL', 'GREENLAND'),
        ('GRD', 'GRENADA'),
        ('GLP', 'GUADELOUPE'),
        ('GUM', 'GUAM'),
        ('GTM', 'GUATEMALA'),
        ('GGY', 'GUERNSEY'),
        ('GIN', 'GUINEA'),
        ('GNB', 'GUINEA-BISSAU'),
        ('GUY', 'GUYANA'),
        ('HTI', 'HAITI'),
        ('HMD', 'HEARD ISLAND AND MCDONALD ISLANDS'),
        ('HND', 'HONDURAS'),
        ('HKG', 'HONG KONG'),
        ('HUN', 'HUNGARY'),
        ('ISL', 'ICELAND'),
        ('IND', 'INDIA'),
        ('IDN', 'INDONESIA'),
        ('IRN', 'IRAN'),
        ('IRQ', 'IRAQ'),
        ('IRL', 'IRELAND'),
        ('IMN', 'ISLE OF MAN'),
        ('ISR', 'ISRAEL'),
        ('ITA', 'ITALY'),
        ('CIV', 'IVORY COAST'),
        ('JAM', 'JAMAICA'),
        ('JPN', 'JAPAN'),
        ('JEY', 'JERSEY'),
        ('JOR', 'JORDAN'),
        ('KAZ', 'KAZAKHSTAN'),
        ('KEN', 'KENYA'),
        ('KIR', 'KIRIBATI'),
        ('UNK', 'KOSOVO'),
        ('KWT', 'KUWAIT'),
        ('KGZ', 'KYRGYZSTAN'),
        ('LAO', 'LAOS'),
        ('LVA', 'LATVIA'),
        ('LBN', 'LEBANON'),
        ('LSO', 'LESOTHO'),
        ('LBR', 'LIBERIA'),
        ('LBY', 'LIBYA'),
        ('LIE', 'LIECHTENSTEIN'),
        ('LTU', 'LITHUANIA'),
        ('LUX', 'LUXEMBOURG'),
        ('MAC', 'MACAU'),
        ('MDG', 'MADAGASCAR'),
        ('MWI', 'MALAWI'),
        ('MYS', 'MALAYSIA'),
        ('MDV', 'MALDIVES'),
        ('MLI', 'MALI'),
        ('MLT', 'MALTA'),
        ('MHL', 'MARSHALL ISLANDS'),
        ('MTQ', 'MARTINIQUE'),
        ('MRT', 'MAURITANIA'),
        ('MUS', 'MAURITIUS'),
        ('MYT', 'MAYOTTE'),
        ('MEX', 'MEXICO'),
        ('FSM', 'MICRONESIA'),
        ('MDA', 'MOLDOVA'),
        ('MCO', 'MONACO'),
        ('MNG', 'MONGOLIA'),
        ('MNE', 'MONTENEGRO'),
        ('MSR', 'MONTSERRAT'),
        ('MAR', 'MOROCCO'),
        ('MOZ', 'MOZAMBIQUE'),
        ('MMR', 'MYANMAR'),
        ('NAM', 'NAMIBIA'),
        ('NRU', 'NAURU'),
        ('NPL', 'NEPAL'),
        ('NLD', 'NETHERLANDS'),
        ('NCL', 'NEW CALEDONIA'),
        ('NZL', 'NEW ZEALAND'),
        ('NIC', 'NICARAGUA'),
        ('NER', 'NIGER'),
        ('NGA', 'NIGERIA'),
        ('NIU', 'NIUE'),
        ('NFK', 'NORFOLK ISLAND'),
        ('PRK', 'NORTH KOREA'),
        ('MKD', 'NORTH MACEDONIA'),
        ('MNP', 'NORTHERN MARIANA ISLANDS'),
        ('NOR', 'NORWAY'),
        ('OMN', 'OMAN'),
        ('PAK', 'PAKISTAN'),
        ('PLW', 'PALAU'),
        ('PSE', 'PALESTINE'),
        ('PAN', 'PANAMA'),
        ('PNG', 'PAPUA NEW GUINEA'),
        ('PRY', 'PARAGUAY'),
        ('PER', 'PERU'),
        ('PHL', 'PHILIPPINES'),
        ('PCN', 'PITCAIRN ISLANDS'),
        ('POL', 'POLAND'),
        ('PRT', 'PORTUGAL'),
        ('PRI', 'PUERTO RICO'),
        ('QAT', 'QATAR'),
        ('COG', 'REPUBLIC OF THE CONGO'),
        ('REU', 'RÉUNION'),
        ('ROU', 'ROMANIA'),
        ('RUS', 'RUSSIA'),
        ('RWA', 'RWANDA'),
        ('BLM', 'SAINT BARTHÉLEMY'),
        ('SHN', 'SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA'),
        ('KNA', 'SAINT KITTS AND NEVIS'),
        ('LCA', 'SAINT LUCIA'),
        ('MAF', 'SAINT MARTIN'),
        ('SPM', 'SAINT PIERRE AND MIQUELON'),
        ('VCT', 'SAINT VINCENT AND THE GRENADINES'),
        ('WSM', 'SAMOA'),
        ('SMR', 'SAN MARINO'),
        ('STP', 'SÃO TOMÉ AND PRÍNCIPE'),
        ('SAU', 'SAUDI ARABIA'),
        ('SEN', 'SENEGAL'),
        ('SRB', 'SERBIA'),
        ('SYC', 'SEYCHELLES'),
        ('SLE', 'SIERRA LEONE'),
        ('SGP', 'SINGAPORE'),
        ('SXM', 'SINT MAARTEN'),
        ('SVK', 'SLOVAKIA'),
        ('SVN', 'SLOVENIA'),
        ('SLB', 'SOLOMON ISLANDS'),
        ('SOM', 'SOMALIA'),
        ('ZAF', 'SOUTH AFRICA'),
        ('SGS', 'SOUTH GEORGIA'),
        ('KOR', 'SOUTH KOREA'),
        ('SSD', 'SOUTH SUDAN'),
        ('ESP', 'SPAIN'),
        ('LKA', 'SRI LANKA'),
        ('SDN', 'SUDAN'),
        ('SUR', 'SURINAME'),
        ('SJM', 'SVALBARD AND JAN MAYEN'),
        ('SWE', 'SWEDEN'),
        ('CHE', 'SWITZERLAND'),
        ('SYR', 'SYRIA'),
        ('TWN', 'TAIWAN'),
        ('TJK', 'TAJIKISTAN'),
        ('TZA', 'TANZANIA'),
        ('THA', 'THAILAND'),
        ('TLS', 'TIMOR-LESTE'),
        ('TGO', 'TOGO'),
        ('TKL', 'TOKELAU'),
        ('TON', 'TONGA'),
        ('TTO', 'TRINIDAD AND TOBAGO'),
        ('TUN', 'TUNISIA'),
        ('TUR', 'TURKEY'),
        ('TKM', 'TURKMENISTAN'),
        ('TCA', 'TURKS AND CAICOS ISLANDS'),
        ('TUV', 'TUVALU'),
        ('UGA', 'UGANDA'),
        ('UKR', 'UKRAINE'),
        ('ARE', 'UNITED ARAB EMIRATES'),
        ('GBR', 'UNITED KINGDOM'),
        ('USA', 'UNITED STATES'),
        ('UMI', 'UNITED STATES MINOR OUTLYING ISLANDS'),
        ('VIR', 'UNITED STATES VIRGIN ISLANDS'),
        ('URY', 'URUGUAY'),
        ('UZB', 'UZBEKISTAN'),
        ('VUT', 'VANUATU'),
        ('VAT', 'VATICAN CITY'),
        ('VEN', 'VENEZUELA'),
        ('VNM', 'VIETNAM'),
        ('WLF', 'WALLIS AND FUTUNA'),
        ('ESH', 'WESTERN SAHARA'),
        ('YEM', 'YEMEN'),
        ('ZMB', 'ZAMBIA'),
        ('ZWE', 'ZIMBABWE')
    ]
    country_of_birth = models.CharField(max_length=100, choices=country_of_birth_choices)
    
    date_of_birth = models.DateField()
    
    nationality_choices = [
        ('AFG', 'AFGHANISTAN'),
        ('ALA', 'ÅLAND ISLANDS'),
        ('ALB', 'ALBANIA'),
        ('DZA', 'ALGERIA'),
        ('ASM', 'AMERICAN SAMOA'),
        ('AND', 'ANDORRA'),
        ('AGO', 'ANGOLA'),
        ('AIA', 'ANGUILLA'),
        ('ATA', 'ANTARCTICA'),
        ('ATG', 'ANTIGUA AND BARBUDA'),
        ('ARG', 'ARGENTINA'),
        ('ARM', 'ARMENIA'),
        ('ABW', 'ARUBA'),
        ('AUS', 'AUSTRALIA'),
        ('AUT', 'AUSTRIA'),
        ('AZE', 'AZERBAIJAN'),
        ('BHS', 'BAHAMAS'),
        ('BHR', 'BAHRAIN'),
        ('BGD', 'BANGLADESH'),
        ('BRB', 'BARBADOS'),
        ('BLR', 'BELARUS'),
        ('BEL', 'BELGIUM'),
        ('BLZ', 'BELIZE'),
        ('BEN', 'BENIN'),
        ('BMU', 'BERMUDA'),
        ('BTN', 'BHUTAN'),
        ('BOL', 'BOLIVIA'),
        ('BIH', 'BOSNIA AND HERZEGOVINA'),
        ('BWA', 'BOTSWANA'),
        ('BVT', 'BOUVET ISLAND'),
        ('BRA', 'BRAZIL'),
        ('IOT', 'BRITISH INDIAN OCEAN TERRITORY'),
        ('VGB', 'BRITISH VIRGIN ISLANDS'),
        ('BRN', 'BRUNEI'),
        ('BGR', 'BULGARIA'),
        ('BFA', 'BURKINA FASO'),
        ('BDI', 'BURUNDI'),
        ('KHM', 'CAMBODIA'),
        ('CMR', 'CAMEROON'),
        ('CAN', 'CANADA'),
        ('CPV', 'CAPE VERDE'),
        ('BES', 'CARIBBEAN NETHERLANDS'),
        ('CYM', 'CAYMAN ISLANDS'),
        ('CAF', 'CENTRAL AFRICAN REPUBLIC'),
        ('TCD', 'CHAD'),
        ('CHL', 'CHILE'),
        ('CHN', 'CHINA'),
        ('CXR', 'CHRISTMAS ISLAND'),
        ('CCK', 'COCOS (KEELING) ISLANDS'),
        ('COL', 'COLOMBIA'),
        ('COM', 'COMOROS'),
        ('COK', 'COOK ISLANDS'),
        ('CRI', 'COSTA RICA'),
        ('HRV', 'CROATIA'),
        ('CUB', 'CUBA'),
        ('CUW', 'CURAÇAO'),
        ('CYP', 'CYPRUS'),
        ('CZE', 'CZECHIA'),
        ('DNK', 'DENMARK'),
        ('DJI', 'DJIBOUTI'),
        ('DMA', 'DOMINICA'),
        ('DOM', 'DOMINICAN REPUBLIC'),
        ('COD', 'DR CONGO'),
        ('ECU', 'ECUADOR'),
        ('EGY', 'EGYPT'),
        ('SLV', 'EL SALVADOR'),
        ('GNQ', 'EQUATORIAL GUINEA'),
        ('ERI', 'ERITREA'),
        ('EST', 'ESTONIA'),
        ('SWZ', 'ESWATINI'),
        ('ETH', 'ETHIOPIA'),
        ('FLK', 'FALKLAND ISLANDS'),
        ('FRO', 'FAROE ISLANDS'),
        ('FJI', 'FIJI'),
        ('FIN', 'FINLAND'),
        ('FRA', 'FRANCE'),
        ('GUF', 'FRENCH GUIANA'),
        ('PYF', 'FRENCH POLYNESIA'),
        ('ATF', 'FRENCH SOUTHERN AND ANTARCTIC LANDS'),
        ('GAB', 'GABON'),
        ('GMB', 'GAMBIA'),
        ('GEO', 'GEORGIA'),
        ('DEU', 'GERMANY'),
        ('GHA', 'GHANA'),
        ('GIB', 'GIBRALTAR'),
        ('GRC', 'GREECE'),
        ('GRL', 'GREENLAND'),
        ('GRD', 'GRENADA'),
        ('GLP', 'GUADELOUPE'),
        ('GUM', 'GUAM'),
        ('GTM', 'GUATEMALA'),
        ('GGY', 'GUERNSEY'),
        ('GIN', 'GUINEA'),
        ('GNB', 'GUINEA-BISSAU'),
        ('GUY', 'GUYANA'),
        ('HTI', 'HAITI'),
        ('HMD', 'HEARD ISLAND AND MCDONALD ISLANDS'),
        ('HND', 'HONDURAS'),
        ('HKG', 'HONG KONG'),
        ('HUN', 'HUNGARY'),
        ('ISL', 'ICELAND'),
        ('IND', 'INDIA'),
        ('IDN', 'INDONESIA'),
        ('IRN', 'IRAN'),
        ('IRQ', 'IRAQ'),
        ('IRL', 'IRELAND'),
        ('IMN', 'ISLE OF MAN'),
        ('ISR', 'ISRAEL'),
        ('ITA', 'ITALY'),
        ('CIV', 'IVORY COAST'),
        ('JAM', 'JAMAICA'),
        ('JPN', 'JAPAN'),
        ('JEY', 'JERSEY'),
        ('JOR', 'JORDAN'),
        ('KAZ', 'KAZAKHSTAN'),
        ('KEN', 'KENYA'),
        ('KIR', 'KIRIBATI'),
        ('UNK', 'KOSOVO'),
        ('KWT', 'KUWAIT'),
        ('KGZ', 'KYRGYZSTAN'),
        ('LAO', 'LAOS'),
        ('LVA', 'LATVIA'),
        ('LBN', 'LEBANON'),
        ('LSO', 'LESOTHO'),
        ('LBR', 'LIBERIA'),
        ('LBY', 'LIBYA'),
        ('LIE', 'LIECHTENSTEIN'),
        ('LTU', 'LITHUANIA'),
        ('LUX', 'LUXEMBOURG'),
        ('MAC', 'MACAU'),
        ('MDG', 'MADAGASCAR'),
        ('MWI', 'MALAWI'),
        ('MYS', 'MALAYSIA'),
        ('MDV', 'MALDIVES'),
        ('MLI', 'MALI'),
        ('MLT', 'MALTA'),
        ('MHL', 'MARSHALL ISLANDS'),
        ('MTQ', 'MARTINIQUE'),
        ('MRT', 'MAURITANIA'),
        ('MUS', 'MAURITIUS'),
        ('MYT', 'MAYOTTE'),
        ('MEX', 'MEXICO'),
        ('FSM', 'MICRONESIA'),
        ('MDA', 'MOLDOVA'),
        ('MCO', 'MONACO'),
        ('MNG', 'MONGOLIA'),
        ('MNE', 'MONTENEGRO'),
        ('MSR', 'MONTSERRAT'),
        ('MAR', 'MOROCCO'),
        ('MOZ', 'MOZAMBIQUE'),
        ('MMR', 'MYANMAR'),
        ('NAM', 'NAMIBIA'),
        ('NRU', 'NAURU'),
        ('NPL', 'NEPAL'),
        ('NLD', 'NETHERLANDS'),
        ('NCL', 'NEW CALEDONIA'),
        ('NZL', 'NEW ZEALAND'),
        ('NIC', 'NICARAGUA'),
        ('NER', 'NIGER'),
        ('NGA', 'NIGERIA'),
        ('NIU', 'NIUE'),
        ('NFK', 'NORFOLK ISLAND'),
        ('PRK', 'NORTH KOREA'),
        ('MKD', 'NORTH MACEDONIA'),
        ('MNP', 'NORTHERN MARIANA ISLANDS'),
        ('NOR', 'NORWAY'),
        ('OMN', 'OMAN'),
        ('PAK', 'PAKISTAN'),
        ('PLW', 'PALAU'),
        ('PSE', 'PALESTINE'),
        ('PAN', 'PANAMA'),
        ('PNG', 'PAPUA NEW GUINEA'),
        ('PRY', 'PARAGUAY'),
        ('PER', 'PERU'),
        ('PHL', 'PHILIPPINES'),
        ('PCN', 'PITCAIRN ISLANDS'),
        ('POL', 'POLAND'),
        ('PRT', 'PORTUGAL'),
        ('PRI', 'PUERTO RICO'),
        ('QAT', 'QATAR'),
        ('COG', 'REPUBLIC OF THE CONGO'),
        ('REU', 'RÉUNION'),
        ('ROU', 'ROMANIA'),
        ('RUS', 'RUSSIA'),
        ('RWA', 'RWANDA'),
        ('BLM', 'SAINT BARTHÉLEMY'),
        ('SHN', 'SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA'),
        ('KNA', 'SAINT KITTS AND NEVIS'),
        ('LCA', 'SAINT LUCIA'),
        ('MAF', 'SAINT MARTIN'),
        ('SPM', 'SAINT PIERRE AND MIQUELON'),
        ('VCT', 'SAINT VINCENT AND THE GRENADINES'),
        ('WSM', 'SAMOA'),
        ('SMR', 'SAN MARINO'),
        ('STP', 'SÃO TOMÉ AND PRÍNCIPE'),
        ('SAU', 'SAUDI ARABIA'),
        ('SEN', 'SENEGAL'),
        ('SRB', 'SERBIA'),
        ('SYC', 'SEYCHELLES'),
        ('SLE', 'SIERRA LEONE'),
        ('SGP', 'SINGAPORE'),
        ('SXM', 'SINT MAARTEN'),
        ('SVK', 'SLOVAKIA'),
        ('SVN', 'SLOVENIA'),
        ('SLB', 'SOLOMON ISLANDS'),
        ('SOM', 'SOMALIA'),
        ('ZAF', 'SOUTH AFRICA'),
        ('SGS', 'SOUTH GEORGIA'),
        ('KOR', 'SOUTH KOREA'),
        ('SSD', 'SOUTH SUDAN'),
        ('ESP', 'SPAIN'),
        ('LKA', 'SRI LANKA'),
        ('SDN', 'SUDAN'),
        ('SUR', 'SURINAME'),
        ('SJM', 'SVALBARD AND JAN MAYEN'),
        ('SWE', 'SWEDEN'),
        ('CHE', 'SWITZERLAND'),
        ('SYR', 'SYRIA'),
        ('TWN', 'TAIWAN'),
        ('TJK', 'TAJIKISTAN'),
        ('TZA', 'TANZANIA'),
        ('THA', 'THAILAND'),
        ('TLS', 'TIMOR-LESTE'),
        ('TGO', 'TOGO'),
        ('TKL', 'TOKELAU'),
        ('TON', 'TONGA'),
        ('TTO', 'TRINIDAD AND TOBAGO'),
        ('TUN', 'TUNISIA'),
        ('TUR', 'TURKEY'),
        ('TKM', 'TURKMENISTAN'),
        ('TCA', 'TURKS AND CAICOS ISLANDS'),
        ('TUV', 'TUVALU'),
        ('UGA', 'UGANDA'),
        ('UKR', 'UKRAINE'),
        ('ARE', 'UNITED ARAB EMIRATES'),
        ('GBR', 'UNITED KINGDOM'),
        ('USA', 'UNITED STATES'),
        ('UMI', 'UNITED STATES MINOR OUTLYING ISLANDS'),
        ('VIR', 'UNITED STATES VIRGIN ISLANDS'),
        ('URY', 'URUGUAY'),
        ('UZB', 'UZBEKISTAN'),
        ('VUT', 'VANUATU'),
        ('VAT', 'VATICAN CITY'),
        ('VEN', 'VENEZUELA'),
        ('VNM', 'VIETNAM'),
        ('WLF', 'WALLIS AND FUTUNA'),
        ('ESH', 'WESTERN SAHARA'),
        ('YEM', 'YEMEN'),
        ('ZMB', 'ZAMBIA'),
        ('ZWE', 'ZIMBABWE'),
    ]
    nationality = models.CharField(max_length=100, choices=nationality_choices)
    
    country_of_residence_choices = [
        ('AFG', 'AFGHANISTAN'),
        ('ALA', 'ÅLAND ISLANDS'),
        ('ALB', 'ALBANIA'),
        ('DZA', 'ALGERIA'),
        ('ASM', 'AMERICAN SAMOA'),
        ('AND', 'ANDORRA'),
        ('AGO', 'ANGOLA'),
        ('AIA', 'ANGUILLA'),
        ('ATA', 'ANTARCTICA'),
        ('ATG', 'ANTIGUA AND BARBUDA'),
        ('ARG', 'ARGENTINA'),
        ('ARM', 'ARMENIA'),
        ('ABW', 'ARUBA'),
        ('AUS', 'AUSTRALIA'),
        ('AUT', 'AUSTRIA'),
        ('AZE', 'AZERBAIJAN'),
        ('BHS', 'BAHAMAS'),
        ('BHR', 'BAHRAIN'),
        ('BGD', 'BANGLADESH'),
        ('BLR', 'BELARUS'),
        ('BEL', 'BELGIUM'),
        ('BLZ', 'BELIZE'),
        ('BEN', 'BENIN'),
        ('BMU', 'BERMUDA'),
        ('BTN', 'BHUTAN'),
        ('BOL', 'BOLIVIA'),
        ('BIH', 'BOSNIA AND HERZEGOVINA'),
        ('BWA', 'BOTSWANA'),
        ('BVT', 'BOUVET ISLAND'),
        ('BRA', 'BRAZIL'),
        ('IOT', 'BRITISH INDIAN OCEAN TERRITORY'),
        ('VGB', 'BRITISH VIRGIN ISLANDS'),
        ('BRN', 'BRUNEI'),
        ('BGR', 'BULGARIA'),
        ('BFA', 'BURKINA FASO'),
        ('BDI', 'BURUNDI'),
        ('KHM', 'CAMBODIA'),
        ('CMR', 'CAMEROON'),
        ('CAN', 'CANADA'),
        ('CPV', 'CAPE VERDE'),
        ('BES', 'CARIBBEAN NETHERLANDS'),
        ('CYM', 'CAYMAN ISLANDS'),
        ('CAF', 'CENTRAL AFRICAN REPUBLIC'),
        ('TCD', 'CHAD'),
        ('CHL', 'CHILE'),
        ('CHN', 'CHINA'),
        ('CXR', 'CHRISTMAS ISLAND'),
        ('CCK', 'COCOS (KEELING) ISLANDS'),
        ('COL', 'COLOMBIA'),
        ('COM', 'COMOROS'),
        ('COK', 'COOK ISLANDS'),
        ('CRI', 'COSTA RICA'),
        ('HRV', 'CROATIA'),
        ('CUB', 'CUBA'),
        ('CUW', 'CURAÇAO'),
        ('CYP', 'CYPRUS'),
        ('CZE', 'CZECHIA'),
        ('DNK', 'DENMARK'),
        ('DJI', 'DJIBOUTI'),
        ('DMA', 'DOMINICA'),
        ('DOM', 'DOMINICAN REPUBLIC'),
        ('COD', 'DR CONGO'),
        ('ECU', 'ECUADOR'),
        ('EGY', 'EGYPT'),
        ('SLV', 'EL SALVADOR'),
        ('GNQ', 'EQUATORIAL GUINEA'),
        ('ERI', 'ERITREA'),
        ('EST', 'ESTONIA'),
        ('SWZ', 'ESWATINI'),
        ('ETH', 'ETHIOPIA'),
        ('FLK', 'FALKLAND ISLANDS'),
        ('FRO', 'FAROE ISLANDS'),
        ('FJI', 'FIJI'),
        ('FIN', 'FINLAND'),
        ('FRA', 'FRANCE'),
        ('GUF', 'FRENCH GUIANA'),
        ('PYF', 'FRENCH POLYNESIA'),
        ('ATF', 'FRENCH SOUTHERN AND ANTARCTIC LANDS'),
        ('GAB', 'GABON'),
        ('GMB', 'GAMBIA'),
        ('GEO', 'GEORGIA'),
        ('DEU', 'GERMANY'),
        ('GHA', 'GHANA'),
        ('GIB', 'GIBRALTAR'),
        ('GRC', 'GREECE'),
        ('GRL', 'GREENLAND'),
        ('GRD', 'GRENADA'),
        ('GLP', 'GUADELOUPE'),
        ('GUM', 'GUAM'),
        ('GTM', 'GUATEMALA'),
        ('GGY', 'GUERNSEY'),
        ('GIN', 'GUINEA'),
        ('GNB', 'GUINEA-BISSAU'),
        ('GUY', 'GUYANA'),
        ('HTI', 'HAITI'),
        ('HMD', 'HEARD ISLAND AND MCDONALD ISLANDS'),
        ('HND', 'HONDURAS'),
        ('HKG', 'HONG KONG'),
        ('HUN', 'HUNGARY'),
        ('ISL', 'ICELAND'),
        ('IND', 'INDIA'),
        ('IDN', 'INDONESIA'),
        ('IRN', 'IRAN'),
        ('IRQ', 'IRAQ'),
        ('IRL', 'IRELAND'),
        ('IMN', 'ISLE OF MAN'),
        ('ISR', 'ISRAEL'),
        ('ITA', 'ITALY'),
        ('CIV', 'IVORY COAST'),
        ('JAM', 'JAMAICA'),
        ('JPN', 'JAPAN'),
        ('JEY', 'JERSEY'),
        ('JOR', 'JORDAN'),
        ('KAZ', 'KAZAKHSTAN'),
        ('KEN', 'KENYA'),
        ('KIR', 'KIRIBATI'),
        ('UNK', 'KOSOVO'),
        ('KWT', 'KUWAIT'),
        ('KGZ', 'KYRGYZSTAN'),
        ('LAO', 'LAOS'),
        ('LVA', 'LATVIA'),
        ('LBN', 'LEBANON'),
        ('LSO', 'LESOTHO'),
        ('LBR', 'LIBERIA'),
        ('LBY', 'LIBYA'),
        ('LIE', 'LIECHTENSTEIN'),
        ('LTU', 'LITHUANIA'),
        ('LUX', 'LUXEMBOURG'),
        ('MAC', 'MACAU'),
        ('MDG', 'MADAGASCAR'),
        ('MWI', 'MALAWI'),
        ('MYS', 'MALAYSIA'),
        ('MDV', 'MALDIVES'),
        ('MLI', 'MALI'),
        ('MLT', 'MALTA'),
        ('MHL', 'MARSHALL ISLANDS'),
        ('MTQ', 'MARTINIQUE'),
        ('MRT', 'MAURITANIA'),
        ('MUS', 'MAURITIUS'),
        ('MYT', 'MAYOTTE'),
        ('MEX', 'MEXICO'),
        ('FSM', 'MICRONESIA'),
        ('MDA', 'MOLDOVA'),
        ('MCO', 'MONACO'),
        ('MNG', 'MONGOLIA'),
        ('MNE', 'MONTENEGRO'),
        ('MSR', 'MONTSERRAT'),
        ('MAR', 'MOROCCO'),
        ('MOZ', 'MOZAMBIQUE'),
        ('MMR', 'MYANMAR'),
        ('NAM', 'NAMIBIA'),
        ('NRU', 'NAURU'),
        ('NPL', 'NEPAL'),
        ('NLD', 'NETHERLANDS'),
        ('NCL', 'NEW CALEDONIA'),
        ('NZL', 'NEW ZEALAND'),
        ('NIC', 'NICARAGUA'),
        ('NER', 'NIGER'),
        ('NGA', 'NIGERIA'),
        ('NIU', 'NIUE'),
        ('NFK', 'NORFOLK ISLAND'),
        ('PRK', 'NORTH KOREA'),
        ('MKD', 'NORTH MACEDONIA'),
        ('MNP', 'NORTHERN MARIANA ISLANDS'),
        ('NOR', 'NORWAY'),
        ('OMN', 'OMAN'),
        ('PAK', 'PAKISTAN'),
        ('PLW', 'PALAU'),
        ('PSE', 'PALESTINE'),
        ('PAN', 'PANAMA'),
        ('PNG', 'PAPUA NEW GUINEA'),
        ('PRY', 'PARAGUAY'),
        ('PER', 'PERU'),
        ('PHL', 'PHILIPPINES'),
        ('PCN', 'PITCAIRN ISLANDS'),
        ('POL', 'POLAND'),
        ('PRT', 'PORTUGAL'),
        ('PRI', 'PUERTO RICO'),
        ('QAT', 'QATAR'),
        ('COG', 'REPUBLIC OF THE CONGO'),
        ('REU', 'RÉUNION'),
        ('ROU', 'ROMANIA'),
        ('RUS', 'RUSSIA'),
        ('RWA', 'RWANDA'),
        ('BLM', 'SAINT BARTHÉLEMY'),
        ('SHN', 'SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA'),
        ('KNA', 'SAINT KITTS AND NEVIS'),
        ('LCA', 'SAINT LUCIA'),
        ('MAF', 'SAINT MARTIN'),
        ('SPM', 'SAINT PIERRE AND MIQUELON'),
        ('VCT', 'SAINT VINCENT AND THE GRENADINES'),
        ('WSM', 'SAMOA'),
        ('SMR', 'SAN MARINO'),
        ('STP', 'SÃO TOMÉ AND PRÍNCIPE'),
        ('SAU', 'SAUDI ARABIA'),
        ('SEN', 'SENEGAL'),
        ('SRB', 'SERBIA'),
        ('SYC', 'SEYCHELLES'),
        ('SLE', 'SIERRA LEONE'),
        ('SGP', 'SINGAPORE'),
        ('SXM', 'SINT MAARTEN'),
        ('SVK', 'SLOVAKIA'),
        ('SVN', 'SLOVENIA'),
        ('SLB', 'SOLOMON ISLANDS'),
        ('SOM', 'SOMALIA'),
        ('ZAF', 'SOUTH AFRICA'),
        ('SGS', 'SOUTH GEORGIA'),
        ('KOR', 'SOUTH KOREA'),
        ('SSD', 'SOUTH SUDAN'),
        ('ESP', 'SPAIN'),
        ('LKA', 'SRI LANKA'),
        ('SDN', 'SUDAN'),
        ('SUR', 'SURINAME'),
        ('SJM', 'SVALBARD AND JAN MAYEN'),
        ('SWE', 'SWEDEN'),
        ('CHE', 'SWITZERLAND'),
        ('SYR', 'SYRIA'),
        ('TWN', 'TAIWAN'),
        ('TJK', 'TAJIKISTAN'),
        ('TZA', 'TANZANIA'),
        ('THA', 'THAILAND'),
        ('TLS', 'TIMOR-LESTE'),
        ('TGO', 'TOGO'),
        ('TKL', 'TOKELAU'),
        ('TON', 'TONGA'),
        ('TTO', 'TRINIDAD AND TOBAGO'),
        ('TUN', 'TUNISIA'),
        ('TUR', 'TURKEY'),
        ('TKM', 'TURKMENISTAN'),
        ('TCA', 'TURKS AND CAICOS ISLANDS'),
        ('TUV', 'TUVALU'),
        ('UGA', 'UGANDA'),
        ('UKR', 'UKRAINE'),
        ('ARE', 'UNITED ARAB EMIRATES'),
        ('GBR', 'UNITED KINGDOM'),
        ('USA', 'UNITED STATES'),
        ('UMI', 'UNITED STATES MINOR OUTLYING ISLANDS'),
        ('VIR', 'UNITED STATES VIRGIN ISLANDS'),
        ('URY', 'URUGUAY'),
        ('UZB', 'UZBEKISTAN'),
        ('VUT', 'VANUATU'),
        ('VAT', 'VATICAN CITY'),
        ('VEN', 'VENEZUELA'),
        ('VNM', 'VIETNAM'),
        ('WLF', 'WALLIS AND FUTUNA'),
        ('ESH', 'WESTERN SAHARA'),
        ('YEM', 'YEMEN'),
        ('ZMB', 'ZAMBIA'),
        ('ZWE', 'ZIMBABWE'),
    ]
    country_of_residence = models.CharField(max_length=100, choices=country_of_residence_choices)
    
    zip_code = models.CharField(max_length=50)
    phone_number = models.CharField(max_length=50)
    travel_document_number = models.CharField(max_length=100)
    travel_document_expiry = models.DateField()
    visited_countries = models.TextField()
    
    purpose_of_visit_choices = [
        ('Gatherin', 'We Gatherin'),
        ('Surf_pro', 'Surf Pro'),
        ('In transit', 'In-Transit Passenger'),
        ('Barbados Welcome Stamp', 'Barbados Welcome Stamp'),
        ('Business', 'Business'),
        ('Conference/Convention/Meeting', 'Conference/Convention/Meeting'),
        ('Crew', 'Crew'),
        ('Cruise', 'Cruise'),
        ('Education', 'Education'),
        ('Funeral', 'Funeral'),
        ('Honeymoon/Wedding', 'Honeymoon/Wedding'),
        ('Medical Attention', 'Medical Attention'),
        ('Pleasure/Holiday/Vacation', 'Pleasure/Holiday/Vacation'),
        ('Returning National', 'Returning National'),
        ('Sports', 'Sports'),
        ('Visiting Friends/Relatives', 'Visiting Friends/Relatives'),
        ('Other', 'Other')
    ]
    purpose_of_visit = models.CharField(max_length=100, choices=purpose_of_visit_choices)

    accommodation_type_choices = [
        ('BED & BREAKFAST', 'Bed and Breakfast'),
        ('APARTMENT', 'Apartment'),
        ('GUEST HOUSE', 'Guest House'),
        ('HOTEL', 'Hotel'),
        ('PRIVATE HOME', 'Private Home'),
        ('VILLA', 'Villa'),
        ('NOT REQUIRED(COMES IN AND LEAVES SAME DAY)', 'Not Required (Comes in and Leaves Same Day)'),
        ('OTHER', 'Other')
    ]
    accommodation_type = models.CharField(max_length=100, choices=accommodation_type_choices, null=True)

    accommodation_name_choices = [
        # Guest house options
        ('Bayfield House', 'Bayfield House'),
        ('Colleton House', 'Colleton House'),
        ('Crystal Crest Guest House', 'Crystal Crest Guest House'),
        ('Crystal Waters Guest House', 'Crystal Waters Guest House'),
        ('Dolphin Inn Guest House & Blue Dolphin Apartments', 'Dolphin Inn Guest House & Blue Dolphin Apartments'),
        ('Eco Lifestyle Lodge/ Sattva Barbados SRL (formerly known as Sea-U Guest House)', 'Eco Lifestyle Lodge/ Sattva Barbados SRL (formerly known as Sea-U Guest House)'),
        ('Harlem B & B', 'Harlem B & B'),
        ('Hidden Gems B & B', 'Hidden Gems B & B'),
        ('Holders House', 'Holders House'),
        ('Hythe Villa Guest House', 'Hythe Villa Guest House'),
        ('Kingsland Palace Guest House', 'Kingsland Palace Guest House'),
        ('Lone Star', 'Lone Star'),
        ('Maraval Guest House', 'Maraval Guest House'),
        ('Melbourne Inn', 'Melbourne Inn'),
        ('Merriville Guest House', 'Merriville Guest House'),
        ('Palm Paradise Guest House', 'Palm Paradise Guest House'),
        ('Rio Guest House', 'Rio Guest House'),
        ('Round House Inn', 'Round House Inn'),
        ('Shonlan Inn & Apartments', 'Shonlan Inn & Apartments'),
        ('Villa Marie', 'Villa Marie'),
        ('Walsh\'s Guest House', 'Walsh\'s Guest House'),
        ('Accra Beach Hotel & Spa', 'Accra Beach Hotel & Spa'),
        # hotel options
        ('All Seasons Resort-Europa', 'All Seasons Resort-Europa'),
        ('Atlantis Hotel & Restaurant', 'Atlantis Hotel & Restaurant'),
        ('Barbados Beach Club', 'Barbados Beach Club'),
        ('Beach View Hotel', 'Beach View Hotel'),
        ('Blue Horizon Hotel Gems of Barbados', 'Blue Horizon Hotel Gems of Barbados'),
        ('Blue Orchids Beach Hotel', 'Blue Orchids Beach Hotel'),
        ('Bougainvillea Beach Resort', 'Bougainvillea Beach Resort'),
        ('Butterfly Beach Hotel', 'Butterfly Beach Hotel'),
        ('Cobblers Cove Hotel', 'Cobblers Cove Hotel'),
        ('Coconut Court Beach Hotel', 'Coconut Court Beach Hotel'),
        ('Colony Club', 'Colony Club'),
        ('Coral Mist Beach Hotel', 'Coral Mist Beach Hotel'),
        ('Coral Reef Club', 'Coral Reef Club'),
        ('Coral Sands Beach Resort', 'Coral Sands Beach Resort'),
        ('Courtyard by Marriott Bridgetown', 'Courtyard by Marriott Bridgetown'),
        ('Crystal Cove Hotel', 'Crystal Cove Hotel'),
        ('Divi Southwinds Beach Resort', 'Divi Southwinds Beach Resort'),
        ('Dover Beach Hotel', 'Dover Beach Hotel'),
        ('Fairmont Royal Pavilion Hotel', 'Fairmont Royal Pavilion Hotel'),
        ('Golden Sands Hotel', 'Golden Sands Hotel'),
        ('Hilton Barbados Resort', 'Hilton Barbados Resort'),
        ('Hotel Pommarine (Barbados Community College Hospitality Institute)', 'Hotel Pommarine (Barbados Community College Hospitality Institute)'),
        ('Infinity on the Beach', 'Infinity on the Beach'),
        ('Island Inn Hotel', 'Island Inn Hotel'),
        ('Little Arches Boutique Hotel', 'Little Arches Boutique Hotel'),
        ('Little Good Harbour Hotel', 'Little Good Harbour Hotel'),
        ('Mango Bay Hotel', 'Mango Bay Hotel'),
        ('Ocean Blue Resort', 'Ocean Blue Resort'),
        ('Ocean 15 Hotel', 'Ocean 15 Hotel'),
        ('O2 Beach Club & Spa', 'O2 Beach Club & Spa'),
        ('Palm Garden Hotel', 'Palm Garden Hotel'),
        ('Palms Resort & Travellers Palm', 'Palms Resort & Travellers Palm'),
        ('Port Ferdinand Marina & Luxury Residences', 'Port Ferdinand Marina & Luxury Residences'),
        ('Radisson Aquatica Resort Barbados', 'Radisson Aquatica Resort Barbados'),
        ('Rostrevor Hotel', 'Rostrevor Hotel'),
        ('Royal Westmoreland Golf & Country Club', 'Royal Westmoreland Golf & Country Club'),
        ("Saint Peter's Bay", "Saint Peter's Bay"),
        ('Sandals Barbados Resort & Spa', 'Sandals Barbados Resort & Spa'),
        ('Sandpiper Hotel (The)', 'Sandpiper Hotel (The)'),
        ('Sandy Lane Hotel', 'Sandy Lane Hotel'),
        ('Savannah Beach Hotel (The)', 'Savannah Beach Hotel (The)'),
        ('Sea Breeze Beach House', 'Sea Breeze Beach House'),
        ('South Gap Hotel', 'South Gap Hotel'),
        ('South Point Hotel', 'South Point Hotel'),
        ('Southern Palms Beach Club', 'Southern Palms Beach Club'),
        ('Sugar Bay Barbados', 'Sugar Bay Barbados'),
        ('Sugar Cane Club Hotel & Spa', 'Sugar Cane Club Hotel & Spa'),
        ('Sunbay Hotel', 'Sunbay Hotel'),
        ('Sweetfield Manor', 'Sweetfield Manor'),
        ('Tamarind Hotel', 'Tamarind Hotel'),
        ('The Abidah by Accra', 'The Abidah by Accra'),
        ('The Club Barbados Resort & Spa', 'The Club Barbados Resort & Spa'),
        ('The Crane Resort', 'The Crane Resort'),
        ('The House', 'The House'),
        ('The Palms Resort', 'The Palms Resort'),
        ('The Rockley', 'The Rockley'),
        ('The Sands', 'The Sands'),
        ('The SoCo Hotel', 'The SoCo Hotel'),
        ('Time Out Hotel', 'Time Out Hotel'),
        ('Treasure Beach Hotel', 'Treasure Beach Hotel'),
        ('Tropical Sunset Beach Apartment Hotel', 'Tropical Sunset Beach Apartment Hotel'),
        ('Tropical Winds Apartment Hotel', 'Tropical Winds Apartment Hotel'),
        ('Turtle Beach Resort', 'Turtle Beach Resort'),
        ('Waves Hotel & Spa by Elegant Hotels', 'Waves Hotel & Spa by Elegant Hotels'),
        ('Worthing Court Apartment Hotel', 'Worthing Court Apartment Hotel'),
        ("Wyndham Grand Barbados Sam Lord's Castle", "Wyndham Grand Barbados Sam Lord's Castle"),
        ('Yellow Bird Hotel', 'Yellow Bird Hotel'),
        # Apartment options
        ('67 Palm Crest Apartments', '67 Palm Crest Apartments'),
        ('Adelcrombie Beach House', 'Adelcrombie Beach House'),
        ('Adulo Apartments', 'Adulo Apartments'),
        ('Annjenn Apartments', 'Annjenn Apartments'),
        ('Ascot House', 'Ascot House'),
        ('Beach Side Apartments 10', 'Beach Side Apartments 10'),
        ('Best E Villas - Prospect', 'Best E Villas - Prospect'),
        ('Best E. Villas - Providence', 'Best E. Villas - Providence'),
        ('Blue Ocean Cottage', 'Blue Ocean Cottage'),
        ("Bourne's Gentle Breeze Apartments", "Bourne's Gentle Breeze Apartments"),
        ('Carib Blue Apartments', 'Carib Blue Apartments'),
        ('Chateau Blanc Apts On Sea', 'Chateau Blanc Apts On Sea'),
        ('Cherry Garden Villa', 'Cherry Garden Villa'),
        ('Cherry Tree Apartments Limited', 'Cherry Tree Apartments Limited'),
        ('Clearwater Apartments', 'Clearwater Apartments'),
        ('Cotton Beach Apartments', 'Cotton Beach Apartments'),
        ("Cumber's Tropical Apartments", "Cumber's Tropical Apartments"),
        ('Dover Woods Apartments', 'Dover Woods Apartments'),
        ('Frontline Apartments', 'Frontline Apartments'),
        ('Gibbs Glade Cottage & Garden Studios', 'Gibbs Glade Cottage & Garden Studios'),
        ('Halcyon Palm Apartments', 'Halcyon Palm Apartments'),
        ('Healthy Horizons Apartment', 'Healthy Horizons Apartment'),
        ('Inchcape Seaside Villas', 'Inchcape Seaside Villas'),
        ('Khrysann - Kingsland', 'Khrysann - Kingsland'),
        ('Kings Beach Village', 'Kings Beach Village'),
        ('Lantana Resort (The)', 'Lantana Resort (The)'),
        ('Legend Gardens Condos (The)', 'Legend Gardens Condos (The)'),
        ('Lighthouse Look Apartments', 'Lighthouse Look Apartments'),
        ('Magic Isle Beach Apartments', 'Magic Isle Beach Apartments'),
        ('Maresol Beach Condominiums', 'Maresol Beach Condominiums'),
        ('Marlane Apartments', 'Marlane Apartments'),
        ('Maxwell Park Apartments', 'Maxwell Park Apartments'),
        ('Melrose Beach Apartments', 'Melrose Beach Apartments'),
        ('Meridian Inn', 'Meridian Inn'),
        ('Miami Beach Apartments', 'Miami Beach Apartments'),
        ('Mirabelle Apartments', 'Mirabelle Apartments'),
        ('Monteray Apartments', 'Monteray Apartments'),
        ('Moonraker Hotel', 'Moonraker Hotel'),
        ('Mullins Grove Apartment Hotel', 'Mullins Grove Apartment Hotel'),
        ('Naniki Barbados (formerly Lush Life Nature Resort)', 'Naniki Barbados (formerly Lush Life Nature Resort)'),
        ('Nautilus Beach Apartments', 'Nautilus Beach Apartments'),
        ('Ocean Bliss Apartments', 'Ocean Bliss Apartments'),
        ('Ocean Sky Apartments', 'Ocean Sky Apartments'),
        ('Ocean Spray Apartments', 'Ocean Spray Apartments'),
        ('Pantherra Terra', 'Pantherra Terra'),
        ('Paradise Villas', 'Paradise Villas'),
        ('Pirates Inn', 'Pirates Inn'),
        ('Plum Tree Club on Rockley Golf Course', 'Plum Tree Club on Rockley Golf Course'),
        ('Port St. Charles', 'Port St. Charles'),
        ('Regent Apartments', 'Regent Apartments'),
        ('Roman Beach Apts.', 'Roman Beach Apts.'),
        ('Rosebank Apartments', 'Rosebank Apartments'),
        ('Sandy Bliss Apts', 'Sandy Bliss Apts'),
        ('Santa Neta Apartments', 'Santa Neta Apartments'),
        ('Santosha Barbados', 'Santosha Barbados'),
        ('Sea Foam Haciendas Apartments', 'Sea Foam Haciendas Apartments'),
        ('Shades Apartment', 'Shades Apartment'),
        ('Southern Surf Beach Apartments', 'Southern Surf Beach Apartments'),
        ("Sun N' Sea", "Sun N' Sea"),
        ('Sweet Jewel Apartments', 'Sweet Jewel Apartments'),
        ('The Terraces Suites', 'The Terraces Suites'),
        ('The View', 'The View'),
        ('Villa Ilfracombe (Bed & Breakfast Barbados Ltd )', 'Villa Ilfracombe (Bed & Breakfast Barbados Ltd )'),
        ('Villa Mia', 'Villa Mia'),
        ('Villa Soilel', 'Villa Soilel'),
        ('West Rock Villas', 'West Rock Villas'),
        ('White Sands Beach Condos', 'White Sands Beach Condos')                    
    ]
    accommodation_name = models.CharField(max_length=100, choices=accommodation_name_choices, null=True, blank=True)
    
    accommodation_address = models.CharField(max_length=200, null=True, blank=True)
    specify_accommodation = models.CharField(max_length=200, null=True, blank=True)
    permament_address = models.CharField(max_length=200, null=True, blank=True)

    parish_choices = [
        ('Christ Church', 'Christ Church'),
        ('St. Andrew', 'St. Andrew'),
        ('St. George', 'St. George'),
        ('St. James', 'St. James'),
        ('St. John', 'St. John'),
        ('St. Joseph', 'St. Joseph'),
        ('St. Lucy', 'St. Lucy'),
        ('St. Michael', 'St. Michael'),
        ('St. Peter', 'St. Peter'),
        ('St. Philip', 'St. Philip'),
        ('St. Thomas', 'St. Thomas'),
        ('Not Applicable', 'Not Applicable')
    ]
    parish = models.CharField(max_length=100, choices=parish_choices)
    
    length_of_stay = models.IntegerField()
    
    farm_question_choices = [
        ('yes', 'Yes'),
        ('no', 'No')
    ]
    farm_question = models.CharField(max_length=100, choices=farm_question_choices)
    
    family_members_choices = [
        ('0', '0'),
        ('1', '1'),
        ('2', '2'),
        ('3', '3'),
        ('4', '4')
    ]
    family_members = models.CharField(max_length=20, choices=family_members_choices, default='0')
    
    lugguage = models.IntegerField()
    
    customs_question_1_choices = [
        ('YES', 'Yes'),
        ('NO', 'No')
    ]
    customs_question_1 = models.CharField(max_length=100, choices=customs_question_1_choices)

    customs_question_2_choices = [
        ('YES', 'Yes'),
        ('NO', 'No')
    ]
    customs_question_2 = models.CharField(max_length=100, choices=customs_question_2_choices)

    customs_question_3_choices = [
        ('YES', 'Yes'),
        ('NO', 'No')
    ]  
    customs_question_3 = models.CharField(max_length=100, choices=customs_question_3_choices)

    customs_question_4_choices = [
        ('YES', 'Yes'),
        ('NO', 'No')
    ]
    customs_question_4 = models.CharField(max_length=100, choices=customs_question_4_choices)

    customs_question_5_choices = [
        ('YES', 'Yes'),
        ('NO', 'No')
    ]
    customs_question_5 = models.CharField(max_length=100, choices=customs_question_5_choices)

    passenger_allowance_choices = [
        ('YES', 'Yes'),
        ('NO', 'No')
    ]
    passenger_allowance = models.CharField(max_length=100, choices=passenger_allowance_choices)

    




# Barbados family members model ------------------------------------------------

class BarbadosFamilyMembers(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    barbados_form = models.ForeignKey(BarbadosForm, on_delete=models.CASCADE, related_name='barbados_family_members')
    first_name = models.CharField(max_length=100)
    surname = models.CharField(max_length=100)
    
    gender_choices = [
        ('male', 'Male'),
        ('female', 'Female')
    ]
    gender = models.CharField(max_length=100, choices=gender_choices)
    
    date_of_birth = models.DateField()
    nationality = models.CharField(max_length=100)
    travel_document_number = models.CharField(max_length=100)
    travel_document_expiry = models.DateField()
    visited_countries = models.TextField()
    
    farm_question_choices = [
        ('yes', 'Yes'),
        ('no', 'No')
    ]
    farm_question = models.CharField(max_length=100, choices=farm_question_choices)

    def __str__(self):
        return f"{self.first_name} {self.surname} - Family member of: {self.barbados_form.order.first_name} {self.barbados_form.order.surname} (Order ID: {self.barbados_form.order.id})"

    class Meta:
        verbose_name = "Barbados Family Member"
        verbose_name_plural = "Barbados Family Members"
