# Generated by Django 5.2 on 2025-04-21 22:42

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("locations", "0002_alter_location_options"),
    ]

    operations = [
        migrations.AddField(
            model_name="location",
            name="application_price_GBP",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="application_price_native",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="conversion_rate_gbp",
            field=models.DecimalField(
                blank=True,
                decimal_places=6,
                help_text="Rate to convert from native currency to GBP",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="currency",
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
    ]
