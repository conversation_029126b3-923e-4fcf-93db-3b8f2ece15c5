from celery.signals import task_prerun

@task_prerun.connect
def validate_task(sender, task_id, task, args, kwargs, **extras):
    """Validate incoming tasks with appropriate exceptions."""
    # List of tasks that are allowed to have no arguments
    NO_ARG_TASKS = {
        'queue_system.tasks.sync_worker_status_task',
        'queue_system.tasks.check_waiting_queue',
        'queue_system.tasks.process_queued_jobs'
    }
    
    # Skip validation for allowed no-arg tasks
    if sender.name in NO_ARG_TASKS:
        return
        
    # Validate other tasks
    if not args and not kwargs:
        raise ValueError(f"Task {sender.name} received with no arguments (expected some)")
    
    if not isinstance(task, sender.app.tasks[sender.name]):
        raise TypeError(f"Task type mismatch for {sender.name}")