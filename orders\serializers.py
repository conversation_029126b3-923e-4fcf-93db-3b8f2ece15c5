from rest_framework import serializers
from .models import order, Location

class OrderSerializer(serializers.ModelSerializer):
    location_name = serializers.CharField(source='location.location_name', read_only=True)

    class Meta:
        model = order
        fields = [
            'id',
            'first_name',
            'surname',
            'customer_email', 
            'location',
            'location_name', 
            'status', 
            'created_at', 
            'updated_at'
        ]

class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = [
            'id', 
            'location_name', 
            'description', 
            'traveller_price', 
            'cost_price',
        ]
        
