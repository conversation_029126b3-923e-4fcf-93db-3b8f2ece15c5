# Setting up Celery with Redis

## 1. Install Redis
For Windows:
- Download and install Redis from https://github.com/microsoftarchive/redis/releases
- Start Redis server by running `redis-server.exe`

For Linux/Mac:
```bash
sudo apt-get install redis-server  # Ubuntu/Debian
brew install redis                 # Mac
sudo systemctl start redis         # Start Redis on Linux
```w

## 2. Configure Celery in Django

Make sure your `config/settings.py` has the following:

```python
# Celery Configuration
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'UTC'
```

## 3. Start Celery Workers

In a separate terminal, run:

```bash
# Start a worker for all queues
celery -A config worker -l info

# Or start workers for specific queues
celery -A config worker -Q location.barbados -l info
```

## 4. Start Celery Beat (for scheduled tasks)

In another terminal, run:

```bash
celery -A config beat -l info
```